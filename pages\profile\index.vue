<template>
	<view class="container">
		<!-- 认证等待状态 -->
		<view v-if="!showPageContent" class="auth-waiting">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ pageStatusText }}</text>
			</view>
		</view>

		<!-- 页面主要内容 - 只有认证成功后才显示 -->
		<view v-if="showPageContent" class="main-content">
			<!-- 用户头像区域 -->
			<!-- <view class="avatar-section" @click="goToAvatarEdit" @tap="goToAvatarEdit">
				<view class="avatar-container">
					<image
						class="avatar"
						:src="(userInfo && userInfo.avatar) || defaultAvatar"
						mode="aspectFill"
						@error="onAvatarError"
					></image>
					<view class="edit-overlay">
						<text class="edit-icon">✏️</text>
					</view>
				</view>
				<text class="welcome-text">欢迎回来</text>
				<text class="edit-hint">点击编辑头像</text>
			</view> -->

			<!-- 用户信息卡片 -->
			<view class="user-info-card">
				<!-- 头像、昵称、角色一行布局 -->
				<view class="profile-row" @click="goToAvatarEdit" @tap="goToAvatarEdit">
					<view class="profile-left">
						<image
							class="profile-avatar"
							:src="(userInfo && userInfo.avatar) || defaultAvatar"
							mode="aspectFill"
							@error="onAvatarError"
						></image>
						<view class="profile-nickname">
							<text class="nickname-text">{{ (userInfo && userInfo.nickname) || '未设置' }}</text>
							<text class="edit-hint-small">点击编辑</text>
						</view>
					</view>
					<view class="profile-right">
						<text class="role-badge">{{ (userInfo && userInfo.role && userInfo.role.name) || '普通用户' }}</text>
					</view>
				</view>

				<view class="info-item" v-if="userInfo && userInfo.phone">
					<view class="info-label">
						<text class="label-icon">📱</text>
						<text class="label-text">手机号</text>
					</view>
					<text class="info-value">{{ formatPhone(userInfo.phone) }}</text>
				</view>
			</view>

			<!-- 管理栏目区域 -->
			<view v-if="getManagementOptions.length > 0" class="management-section">
				<view class="management-card">
					<view class="management-header">
						<text class="management-title">管理功能</text>
						<text class="management-subtitle">根据您的权限显示</text>
					</view>

					<view class="management-list">
						<view
							v-for="(option, index) in getManagementOptions"
							:key="index"
							class="management-item"
							@click="handleManagementClick(option)"
							@tap="handleManagementClick(option)"
						>
							<view class="management-info">
								<text class="management-icon">{{ option.icon }}</text>
								<view class="management-text">
									<text class="management-name">{{ option.title }}</text>
									<text class="management-desc">{{ option.description }}</text>
								</view>
							</view>
							<text class="management-arrow">›</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 功能栏目区域 -->
			<view class="function-section">
				<view class="function-card">
					<view class="function-header">
						<text class="function-title">功能服务</text>
						<text class="function-subtitle">常用功能快捷入口</text>
					</view>

					<view class="function-list">
						<view
							class="function-item"
							@click="goToPreSeedLog"
							@tap="goToPreSeedLog"
						>
							<view class="function-info">
								<text class="function-icon">🌱</text>
								<view class="function-text">
									<text class="function-name">试水苗领取</text>
									<text class="function-desc">扫码领取试水苗</text>
								</view>
							</view>
							<text class="function-arrow">›</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 功能按钮区域 -->
			<view class="action-section">
				<button class="action-btn danger" @click="confirmLogout">
					<text class="btn-icon">🚪</text>
					<text class="btn-text">退出登录</text>
				</button>
			</view>
		</view>

		<!-- 认证失败状态 -->
		<view v-if="authReady && !authSuccess" class="auth-failed">
			<view class="error-container">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{ authError || '登录验证失败' }}</text>
				<text class="error-desc">无法加载用户信息，请重新登录</text>
				<button class="retry-btn" @click="retryAuth">重试</button>
			</view>
		</view>
	</view>
</template>

<script>
import { mapGetters, mapActions, mapState } from 'vuex'
import pageEventMixin from '@/mixins/pageEvent.js'

export default {
	name: 'ProfilePage',
	mixins: [pageEventMixin],

	data() {
		return {
			defaultAvatar: '/static/default-avatar.png'
		}
	},

	computed: {
		...mapGetters('auth', ['isTokenValid']),
		...mapState('user', ['userInfo']),

		/**
		 * 安全获取用户角色代码
		 */
		getRoleCode() {
			if (!this.userInfo || !this.userInfo.role) {
				return null
			}

			// 如果role是对象且包含code字段
			if (typeof this.userInfo.role === 'object' && this.userInfo.role.code) {
				return this.userInfo.role.code
			}

			return null
		},

		/**
		 * 根据角色代码获取管理选项
		 */
		getManagementOptions() {
			const roleCode = this.getRoleCode

			switch (roleCode) {
				case 8888:
					return [
				
						{
							icon: '🏢',
							title: '苗中介',
							type: 'agent_list',
							description: '查看和管理代理列表'
						},
						{
							icon: '🔐',
							title: '员工登录管理',
							type: 'login_management',
							description: '管理员工'
						}
					]
				case 3333:
					return [
							{
								icon: '🌱',
								title: '试水苗管理',
								type: 'preseed_management',
								description: '管理试水苗相关功能'
							}
					]
				case 6666:
					return [
	{
								icon: '🌱',
								title: '试水苗记录',
								type: 'preseed_management',
								description: '试水苗相关功能'
							},
							{
								icon: '🌱',
								title: '体长测量',
								type: 'measure_management',
								description: '管理试水苗相关功能'
							}
					]
				
				default:
					// 所有用户都可以生成邀请码
					return [
						{
							icon: '📨',
							title: '生成邀请码',
							type: 'generate_invite_code',
							description: '生成中介邀请码'
						}
					]
			}
		}
	},

	methods: {
		...mapActions('auth', ['silentLogin', 'clearAuth']),
		...mapActions('user', ['refreshUserInfo']),

		onLoad() {
			console.log('用户资料页面加载')
			// pageEventMixin 会自动设置事件监听
		},

		/**
		 * 认证完成回调 - 由 App.vue 事件触发
		 * 只有认证成功后才会执行
		 */
		onAuthReady(authData) {
			console.log('👤 用户资料页面收到认证完成事件:', authData)
			console.log('✅ 用户资料页面现在可以安全地显示用户信息')
			
			// 认证完成后可以加载额外的用户数据
			this.loadUserProfile()
		},

		/**
		 * 认证失败回调 - 由 App.vue 事件触发
		 */
		onAuthFailed(errorData) {
			console.log('👤 用户资料页面收到认证失败事件:', errorData)
			console.log('❌ 用户资料页面无法显示用户信息，显示错误状态')
		},

		/**
		 * 加载用户资料数据
		 */
		async loadUserProfile() {
			console.log('📊 开始加载用户资料数据')
			
			try {
				// 这里可以加载额外的用户资料信息
				// 例如：用户统计、设置等
				console.log('✅ 用户资料数据加载完成')
			} catch (error) {
				console.error('❌ 用户资料数据加载失败:', error)
			}
		},

		/**
		 * 获取角色名称
		 */
		getRoleName() {
			if (!this.userInfo || !this.userInfo.role) {
				return '普通用户'
			}
			
			if (typeof this.userInfo.role === 'object') {
				return this.userInfo.role.name || this.userInfo.role.role_name || '普通用户'
			}
			
			const roleMap = {
				'user': '普通用户',
				'agent': '代理',
				'miaochang': '苗场主'
			}
			
			return roleMap[this.userInfo.role] || this.userInfo.role || '普通用户'
		},

		/**
		 * 获取角色样式类
		 */
		getRoleClass() {
			const roleName = this.getRoleName()
			if (roleName.includes('苗场主')) return 'role-miaochang'
			if (roleName.includes('代理')) return 'role-agent'
			return 'role-user'
		},

		/**
		 * 格式化手机号
		 */
		formatPhone(phone) {
			if (!phone) return '--'
			const phoneStr = phone.toString()
			if (phoneStr.length === 11) {
				return phoneStr.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
			}
			return phone
		},

		/**
		 * 头像加载失败处理
		 */
		onAvatarError() {
			console.log('头像加载失败，使用默认头像')
		},

		/**
		 * 刷新用户信息
		 */
		async refreshUserInfo() {
			if (!this.canMakeNetworkRequest) {
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			try {
				uni.showLoading({
					title: '刷新中...'
				})

				// 重新执行静默登录以获取最新用户信息
				const result = await this.safeNetworkRequest(this.silentLogin)
				
				if (result.success) {
					uni.showToast({
						title: '刷新成功',
						icon: 'success'
					})
				} else {
					throw new Error(result.error || '刷新失败')
				}
			} catch (error) {
				console.error('刷新用户信息失败:', error)
				uni.showToast({
					title: '刷新失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 显示用户详细信息
		 */
		showUserDetail() {
			if (!this.canExecutePageLogic) {
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			uni.showModal({
				title: '用户详细信息',
				content: JSON.stringify(this.userInfo, null, 2),
				showCancel: false,
				confirmText: '确定'
			})
		},

		/**
		 * 确认退出登录
		 */
		confirmLogout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						this.logout()
					}
				}
			})
		},

		/**
		 * 退出登录
		 */
		async logout() {
			try {
				uni.showLoading({
					title: '退出中...'
				})

				await this.clearAuth()
				
				uni.showToast({
					title: '已退出登录',
					icon: 'success'
				})

				// 跳转到首页
				setTimeout(() => {
					uni.switchTab({
						url: '/pages/index/index'
					})
				}, 1500)

			} catch (error) {
				console.error('退出登录失败:', error)
				uni.showToast({
					title: '退出失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 跳转到头像编辑页面
		 */
		goToAvatarEdit() {
			console.log('🖼️ 跳转到头像编辑页面')

			if (!this.canExecutePageLogic) {
				console.warn('⚠️ 认证未完成，无法跳转')
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			console.log('✅ 认证检查通过，开始跳转到头像编辑页面')
			uni.navigateTo({
				url: '/pages/avatar/index',
				success: () => {
					console.log('✅ 跳转成功')
				},
				fail: (error) => {
					console.error('❌ 跳转失败:', error)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		},

		/**
		 * 跳转到试水苗领取页面
		 */
		goToPreSeedLog() {
			console.log('🌱 跳转到试水苗领取页面')

			uni.navigateTo({
				url: '/pages/PreSeed/logs/index',
				success: () => {
					console.log('✅ 成功跳转到试水苗领取页面')
				},
				fail: (error) => {
					console.error('❌ 跳转失败:', error)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		},

		/**
		 * 重试认证
		 */
		retryAuth() {
			console.log('🔄 用户手动重试认证')
			this.authReady = false
			this.authSuccess = false
			this.authError = null
			this.showPageContent = false
			this.pageLoading = true
			this.waitForAuth()
		},

		/**
		 * 处理管理选项点击
		 */
		handleManagementClick(option) {
			if (!this.canExecutePageLogic) {
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			console.log('🔧 管理选项被点击:', option)

			switch (option.type) {
				case 'agent_management':
					this.goToAgentManagement()
					break
				case 'agent_list':
					this.goToAgentList()
					break
				case 'login_management':
					this.goToLoginManagement()
					break
				case 'preseed_management':
					this.goToPreseedManagement()
					break
				case 'measure_mamagement':
					this.goToAgentManagement()
					break
				case 'generate_invite_code':
					this.goToGenerateInviteCode()
					break
				default:
					uni.showToast({
						title: '功能开发中',
						icon: 'none'
					})
			}
		},

		/**
		 * 跳转到中介管理页面
		 */
		goToAgentManagement() {
			console.log('🏢 跳转到中介管理页面')
			uni.showToast({
				title: '中介管理功能开发中',
				icon: 'none'
			})
			// TODO: 实现跳转逻辑
			// uni.navigateTo({
			//     url: '/pages/management/agent/index'
			// })
		},

		/**
		 * 跳转到代理列表页面
		 */
		goToAgentList() {
			console.log('📋 跳转到代理列表页面')

			if (!this.canExecutePageLogic) {
				console.warn('⚠️ 认证未完成，无法跳转')
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			console.log('✅ 认证检查通过，开始跳转到代理列表页面')
			uni.navigateTo({
				url: '/pages/Agent/list/agentlist',
				success: () => {
					console.log('✅ 跳转成功')
				},
				fail: (error) => {
					console.error('❌ 跳转失败:', error)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		},

		/**
		 * 跳转到登录管理页面（员工列表）
		 */
		goToLoginManagement() {
			console.log('🔐 跳转到员工登录管理页面')
			uni.navigateTo({
				url: '/pages/employee/list/index',
				fail: (error) => {
					console.error('❌ 跳转到员工列表失败:', error)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		},

		/**
		 * 跳转到试水苗管理页面
		 */
		goToPreseedManagement() {
			console.log('🌱 跳转到试水苗管理页面')
			uni.showToast({
				title: '试水苗管理功能开发中',
				icon: 'none'
			})
			// TODO: 实现跳转逻辑
			// uni.navigateTo({
			//     url: '/pages/management/preseed/index'
			// })
		},

		/**
		 * 跳转到生成邀请码页面
		 */
		goToGenerateInviteCode() {
			console.log('📨 跳转到生成邀请码页面')

			if (!this.canExecutePageLogic) {
				console.warn('⚠️ 认证未完成，无法跳转')
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			console.log('✅ 认证检查通过，开始跳转到生成邀请码页面')
			uni.navigateTo({
				url: '/pages/invite/agent/inviteagentpage',
				success: () => {
					console.log('✅ 跳转成功')
				},
				fail: (error) => {
					console.error('❌ 跳转失败:', error)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 认证等待状态样式 */
.auth-waiting {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
	background-color: #fff;
	border-radius: 20rpx;
	margin: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid #e0e0e0;
	border-top: 6rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

/* 主要内容样式 */
.main-content {
	min-height: 100vh;
	padding: 40rpx 30rpx;
}

/* 头像区域 */
.avatar-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 60rpx;
	padding-top: 60rpx;
	cursor: pointer;
	transition: transform 0.2s ease;
}

.avatar-section:active {
	transform: scale(0.98);
}

.avatar-container {
	position: relative;
	margin-bottom: 30rpx;
}

.avatar {
	width: 160rpx;
	height: 160rpx;
	border-radius: 80rpx;
	border: 6rpx solid rgba(255, 255, 255, 0.3);
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
}

.edit-overlay {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 50rpx;
	height: 50rpx;
	background-color: #1890ff;
	border-radius: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.4);
}

.edit-icon {
	font-size: 24rpx;
	color: #fff;
}

.welcome-text {
	font-size: 32rpx;
	color: #fff;
	font-weight: 500;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	margin-bottom: 8rpx;
}

.edit-hint {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 用户信息卡片 */
.user-info-card {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

/* 头像、昵称、角色一行布局 */
.profile-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.profile-row:active {
	background-color: #f8f9fa;
}

.profile-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.profile-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.8);
	margin-right: 20rpx;
}

.profile-nickname {
	display: flex;
	flex-direction: column;
}

.nickname-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
	margin-bottom: 4rpx;
}

.edit-hint-small {
	font-size: 22rpx;
	color: #999;
}

.profile-right {
	display: flex;
	align-items: center;
}

.role-badge {
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #fff;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	font-weight: 500;
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
	border-bottom: none;
}

.info-item.clickable {
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.info-item.clickable:active {
	background-color: #f8f9fa;
}

.info-label {
	display: flex;
	align-items: center;
}

.label-icon {
	font-size: 32rpx;
	margin-right: 15rpx;
}

.label-text {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.info-value-container {
	display: flex;
	align-items: center;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
}

.edit-arrow {
	font-size: 24rpx;
	color: #999;
	margin-left: 12rpx;
}

/* 角色样式 */
.role-value {
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	font-size: 24rpx;
	color: #ffffff;
}

.role-user {
	background-color: #52c41a;
}

.role-agent {
	background-color: #1890ff;
}

.role-miaochang {
	background-color: #722ed1;
}

/* 管理栏目区域样式 */
.management-section {
	margin-bottom: 40rpx;
}

.management-card {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.management-header {
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.management-title {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
	display: block;
	margin-bottom: 8rpx;
}

.management-subtitle {
	font-size: 24rpx;
	color: #999;
	display: block;
}

.management-list {
	display: flex;
	flex-direction: column;
}

.management-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.management-item:last-child {
	border-bottom: none;
}

.management-item:active {
	background-color: #f8f9fa;
}

.management-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.management-icon {
	font-size: 40rpx;
	margin-right: 20rpx;
	width: 60rpx;
	text-align: center;
}

.management-text {
	display: flex;
	flex-direction: column;
	flex: 1;
}

.management-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 6rpx;
}

.management-desc {
	font-size: 24rpx;
	color: #999;
}

.management-arrow {
	font-size: 32rpx;
	color: #ccc;
	font-weight: bold;
}

/* 功能栏目区域样式 */
.function-section {
	margin-bottom: 40rpx;
}

.function-card {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.function-header {
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.function-title {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
	display: block;
	margin-bottom: 8rpx;
}

.function-subtitle {
	font-size: 24rpx;
	color: #999;
	display: block;
}

.function-list {
	display: flex;
	flex-direction: column;
}

.function-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.function-item:last-child {
	border-bottom: none;
}

.function-item:active {
	background-color: #f8f9fa;
}

.function-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.function-icon {
	font-size: 40rpx;
	margin-right: 20rpx;
	width: 60rpx;
	text-align: center;
}

.function-text {
	display: flex;
	flex-direction: column;
	flex: 1;
}

.function-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 6rpx;
}

.function-desc {
	font-size: 24rpx;
	color: #999;
}

.function-arrow {
	font-size: 32rpx;
	color: #ccc;
	font-weight: bold;
}

/* 功能按钮区域 */
.action-section {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.action-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 28rpx;
	font-weight: 500;
	border: none;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.action-btn.primary {
	background-color: #1890ff;
	color: #fff;
}

.action-btn.secondary {
	background-color: #fff;
	color: #1890ff;
	border: 2rpx solid #1890ff;
}

.action-btn.danger {
	background-color: #ff4d4f;
	color: #fff;
}

.btn-icon {
	font-size: 32rpx;
	margin-right: 12rpx;
}

.btn-text {
	font-size: 28rpx;
}

/* 认证失败状态样式 */
.auth-failed {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
	background-color: #fff;
	border-radius: 20rpx;
	margin: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}

.error-text {
	font-size: 28rpx;
	color: #dd524d;
	text-align: center;
	margin-bottom: 20rpx;
}

.error-desc {
	font-size: 24rpx;
	color: #999;
	text-align: center;
	margin-bottom: 40rpx;
}

.retry-btn {
	width: 200rpx;
	height: 70rpx;
	line-height: 70rpx;
	background-color: #667eea;
	color: #fff;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: none;
}
</style>
