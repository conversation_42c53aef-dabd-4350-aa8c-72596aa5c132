<template>
	<view class="employee-list-page">
		<!-- 认证等待状态 -->
		<view v-if="!authReady" class="auth-loading">
			<view class="loading-spinner"></view>
			<text class="loading-text">正在验证身份...</text>
		</view>

		<!-- 认证失败状态 -->
		<view v-else-if="authError" class="auth-error">
			<text class="error-text">{{ authError }}</text>
			<button @click="retryAuth" class="retry-btn">重试</button>
		</view>

		<!-- 页面内容 -->
		<view v-else class="page-content">
			<!-- 员工列表 -->
			<view class="employee-list">
				<view
					v-for="employee in employeeList"
					:key="employee.id"
					class="employee-card"
				>
					<!-- 员工信息 -->
					<view class="employee-info">
						<!-- 头像 - 根据员工自身角色决定是否可点击编辑 -->
						<image
							class="employee-avatar"
							:class="{ 'clickable': canEditEmployee(employee), 'disabled': !canEditEmployee(employee) }"
							:src="employee.avatar || '/static/default-avatar.png'"
							mode="aspectFill"
							@click="canEditEmployee(employee) ? editEmployee(employee) : showEditDisabledTip(employee)"
						/>

						<!-- 基本信息 - 根据员工自身角色决定是否可点击编辑 -->
						<view
							class="employee-details"
							:class="{ 'clickable': canEditEmployee(employee), 'disabled': !canEditEmployee(employee) }"
							@click="canEditEmployee(employee) ? editEmployee(employee) : showEditDisabledTip(employee)"
						>
							<text class="employee-name">{{ employee.nickname || '未设置' }}</text>
							<text class="employee-phone">{{ employee.employeePhone || '未设置' }}</text>
						</view>

						<!-- 登录二维码按钮 -->
						<view class="qr-icon-container" @click="showQrCode(employee)">
							<text class="qr-text">登录二维码</text>
							<text class="qr-icon">📱</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-if="!loading && employeeList.length === 0" class="empty-state">
				<text class="empty-text">暂无员工信息</text>
			</view>

			<!-- 加载状态 -->
			<view v-if="loading" class="loading-more">
				<view class="loading-spinner small"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>


	</view>
</template>

<script>
import { mapGetters } from 'vuex'
import pageEventMixin from '@/mixins/pageEvent.js'
import { userAPI } from '@/api/user.js'

export default {
	name: 'EmployeeListPage',
	mixins: [pageEventMixin],

	data() {
		return {
			employeeList: [],        // 员工列表
			loading: false,          // 是否正在加载
			hasLoadedOnce: false     // 是否已经加载过一次（防止重复调用）
		}
	},

	computed: {
		...mapGetters('user', ['userInfo']),
		...mapGetters('auth', ['isLoggedIn', 'isTokenValid'])
	},

	methods: {
		/**
		 * 检查员工是否可以编辑
		 * 员工角色代码为8888时不能编辑头像
		 */
		canEditEmployee(employee) {
			if (!employee || !employee.role) {
				return true // 如果没有角色信息，默认可以编辑
			}

			// 检查员工的角色代码
			const employeeRoleCode = employee.role.code
			console.log('🔐 检查员工编辑权限:', employee.nickname, '角色代码:', employeeRoleCode)

			// 角色代码为8888的员工不能编辑头像
			return employeeRoleCode !== 8888
		},

		/**
		 * 显示编辑被禁用的提示
		 */
		showEditDisabledTip(employee) {
			console.log('⚠️ 员工编辑被禁用:', employee.nickname, '角色代码:', employee.role?.code)
			uni.showToast({
				title: '老板不允许编辑',
				icon: 'none'
			})
		},

		/**
		 * 加载员工列表
		 */
		async loadEmployeeList() {
			if (!this.canMakeNetworkRequest) {
				console.warn('🚫 认证未完成，无法加载员工列表')
				return
			}

			// 防止重复调用
			if (this.loading) {
				console.warn('🚫 员工列表正在加载中，跳过重复调用')
				return
			}

			try {
				this.loading = true
				console.log('开始加载员工列表')

				const response = await this.safeNetworkRequest(userAPI.getEmployeeList)
				console.log('员工列表API响应:', response)

				// API返回分页格式 {count, next, previous, results}
				if (response && typeof response === 'object' && Array.isArray(response.results)) {
					this.employeeList = response.results || []
					this.hasLoadedOnce = true  // 标记已经加载过
					console.log('员工列表加载成功，总数:', response.count)
				} else {
					console.error('❌ API返回格式错误:', response)
					throw new Error('数据格式错误')
				}
			} catch (error) {
				console.error('❌ 加载员工列表失败:', error)
				uni.showToast({
					title: error.message || '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		/**
		 * 编辑员工信息
		 */
		editEmployee(employee) {
			console.log('✏️ 编辑员工信息:', employee.nickname)
			console.log('📋 完整员工数据:', employee)
			console.log('🆔 员工ID:', employee.id)
			console.log('📱 员工昵称:', employee.nickname)
			console.log('📞 员工电话:', employee.employeePhone)
			console.log('🔐 员工角色:', employee.role)

			// 检查员工数据完整性
			const hasId = employee.id

			if (!hasId) {
				console.warn('⚠️ 员工数据缺少ID字段，但仍允许编辑')
				console.log('📋 员工数据:', employee)
			} else {
				console.log('✅ 员工ID:', hasId)
			}

			// 传递员工JSON数据到编辑页面
			const employeeData = encodeURIComponent(JSON.stringify(employee))
			console.log('📤 传递的员工数据:', employeeData)

			uni.navigateTo({
				url: `/pages/employee/edit/index?employee=${employeeData}`,
				fail: (error) => {
					console.error('❌ 页面跳转失败:', error)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		},

		/**
		 * 显示员工登录码 - 跳转到登录码页面
		 */
		showQrCode(employee) {
			console.log('📱 跳转到员工登录码页面:', employee.nickname)
			console.log('📋 完整员工数据:', employee)
			console.log('🆔 员工ID:', employee.id)

			// 检查员工数据完整性
			if (!employee.id) {
				console.warn('⚠️ 员工数据缺少ID字段')
				uni.showToast({
					title: '员工数据不完整',
					icon: 'none'
				})
				return
			}

			// 传递员工JSON数据到登录码页面
			const employeeData = encodeURIComponent(JSON.stringify(employee))
			console.log('📤 传递的员工数据:', employeeData)

			uni.navigateTo({
				url: `/pages/employee/logincode/index?employee=${employeeData}`,
				fail: (error) => {
					console.error('❌ 页面跳转失败:', error)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		},





		/**
		 * 重试认证
		 */
		retryAuth() {
			console.log('用户手动重试认证')
			this.authReady = false
			this.authError = null
			this.hasLoadedOnce = false  // 重置加载标志
			this.waitForAuth()
		},

		/**
		 * 认证完成回调 - 由 App.vue 事件触发
		 */
		onAuthReady() {
			console.log('员工列表页面收到认证完成事件')

			// 认证完成后自动加载员工列表
			this.loadEmployeeList()
		},

		/**
		 * 认证失败回调 - 由 App.vue 事件触发
		 */
		onAuthFailed(errorData) {
			console.log('员工列表页面收到认证失败事件:', errorData)
		}
	},

	onLoad() {
		console.log('员工列表页面加载')
	},

	onShow() {
		// 如果认证已经完成且成功，但数据还没加载，则手动触发加载
		// 增加 hasLoadedOnce 检查，防止在 onAuthReady 刚调用后立即重复调用
		if (this.authReady && this.authSuccess && this.employeeList.length === 0 && !this.loading && !this.hasLoadedOnce) {
			console.log('检测到认证已完成但数据未加载，手动触发加载')
			this.loadEmployeeList()
		}
	}
}
</script>

<style scoped>
/* 页面容器 */
.employee-list-page {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 认证等待状态 */
.auth-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100vh;
	background-color: #f5f5f5;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #e9ecef;
	border-top: 4rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

.loading-spinner.small {
	width: 40rpx;
	height: 40rpx;
	border-width: 3rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}

/* 认证错误状态 */
.auth-error {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100vh;
	padding: 40rpx;
	background-color: #f5f5f5;
}

.error-text {
	font-size: 32rpx;
	color: #ff4757;
	margin-bottom: 40rpx;
	text-align: center;
}

.retry-btn {
	background-color: #007aff;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

/* 页面内容 */
.page-content {
	padding: 20rpx;
}

/* 员工列表 */
.employee-list {
	margin-bottom: 40rpx;
}

.employee-card {
	background-color: white;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	overflow: hidden;
}

.employee-info {
	display: flex;
	align-items: center;
	padding: 30rpx;
}

.employee-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
	background-color: #f0f0f0;
	transition: opacity 0.3s ease;
}

.employee-avatar.clickable {
	cursor: pointer;
}

.employee-avatar.disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.employee-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	transition: opacity 0.3s ease;
}

.employee-details.clickable {
	cursor: pointer;
}

.employee-details.disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.employee-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.employee-phone {
	font-size: 28rpx;
	color: #666;
}

.qr-icon-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	cursor: pointer;
	min-width: 120rpx;
}

.qr-text {
	font-size: 24rpx;
	color: #007aff;
	margin-bottom: 8rpx;
	text-align: center;
}

.qr-icon {
	font-size: 40rpx;
	color: #007aff;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
	background-color: white;
	border-radius: 16rpx;
	margin: 40rpx 0;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

/* 加载更多状态 */
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}


</style>
