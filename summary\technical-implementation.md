# 技术实现总结

## 1. 状态管理架构 (Vuex)

### 1.1 Store结构设计
**实现位置**: `store/index.js`

```javascript
// 模块化Store架构
const store = new Vuex.Store({
  modules: {
    auth,    // 认证模块
    user     // 用户模块
  },
  strict: process.env.NODE_ENV !== 'production'
})
```

### 1.2 认证模块 (auth)
**实现位置**: `store/modules/auth.js`

#### State设计
```javascript
state: {
  token: null,           // JWT token
  refreshToken: null,    // 刷新token
  lastVerifyTime: null,  // 上次验证时间
  isLoggedIn: false,     // 登录状态
  loginLoading: false,   // 登录加载状态
  hasLaunched: false     // 应用启动状态
}
```

#### 核心Actions
- **silentLogin**: 静默登录主流程
- **verifyToken**: JWT token验证
- **fullLogin**: 完整登录流程
- **saveLocalAuth**: 本地认证信息保存
- **clearAuth**: 清除认证信息

#### Getters设计
```javascript
getters: {
  isTokenValid: (state) => !!state.token && state.isLoggedIn,
  shouldVerifyToken: (state) => {
    const timeDiff = Date.now() - state.lastVerifyTime
    return timeDiff >= 1 * 60 * 1000 // 1分钟检查间隔
  }
}
```

### 1.3 用户模块 (user)
**实现位置**: `store/modules/user.js`

#### 用户信息结构
```javascript
userInfo: {
  id: number,
  nickname: string,
  avatar: string,
  phone: string,
  openid: string,
  role: {
    id: number,
    name: string,
    code: number  // 角色代码：8888, 5555, 3333等
  }
}
```

## 2. 网络请求架构

### 2.1 请求拦截器
**实现位置**: `utils/request.js`

#### 核心功能
```javascript
// 请求拦截 - 自动添加token
header: {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
}

// 响应拦截 - 统一错误处理
if (res.statusCode === 401) {
  // 自动清除认证信息并跳转
  store.dispatch('auth/clearAuth')
  uni.reLaunch({ url: '/pages/index/index' })
}
```

#### 请求去重机制
**实现位置**: `utils/requestDeduplication.js`

```javascript
// 防止重复请求
const requestKey = `${method}_${url}_${JSON.stringify(data)}`
if (pendingRequests.has(requestKey)) {
  return pendingRequests.get(requestKey)
}
```

### 2.2 API适配器
**实现位置**: `utils/apiAdapter.js`

#### 响应数据标准化
```javascript
// JWT验证响应适配
export function adaptJwtVerifyResponse(response) {
  const { jwtToken, user } = response.results
  return {
    success: true,
    token: jwtToken.token,
    refresh: jwtToken.refresh,
    user: normalizeUserData(user)
  }
}

// 用户数据标准化
function normalizeUserData(user) {
  return {
    ...user,
    role: typeof user.role === 'string' 
      ? JSON.parse(user.role) 
      : user.role
  }
}
```

## 3. 路由和页面导航

### 3.1 页面配置
**实现位置**: `pages.json`

#### TabBar设计
```json
{
  "tabBar": {
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页"
      },
      {
        "pagePath": "pages/profile/index", 
        "text": "我的"
      }
    ]
  }
}
```

#### 页面权限配置
- **无需认证**: 激活页面 (ActAgent, ActMiaoChang)
- **需要认证**: 管理页面 (employee, agent, preseed)
- **角色限制**: 特定功能页面基于role.code控制

### 3.2 页面认证机制
**实现位置**: `mixins/pageEvent.js`

#### 认证等待机制
```javascript
// 页面混入 - 等待认证完成
mounted() {
  this.waitForAuth()
},

methods: {
  waitForAuth() {
    uni.$on('auth-ready', this.onAuthReady)
    uni.$on('auth-failed', this.onAuthFailed)
  }
}
```

## 4. 组件复用和工具函数

### 4.1 本地存储工具
**实现位置**: `utils/storage.js`

#### 统一存储接口
```javascript
export const storageUtils = {
  setItem(key, value) {
    uni.setStorageSync(key, value)
  },
  getItem(key, defaultValue = null) {
    return uni.getStorageSync(key) || defaultValue
  },
  removeItem(key) {
    uni.removeStorageSync(key)
  }
}

// 存储键名常量
export const STORAGE_KEYS = {
  AUTH_DATA: 'auth_data',
  USER_INFO: 'user_info',
  APP_CONFIG: 'app_config'
}
```

### 4.2 图片处理工具
**实现位置**: `utils/imageUtils.js`

#### 核心功能
```javascript
// 图片选择并转换为base64
export function chooseImageToBase64(options = {}) {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count: 1,
      sourceType: [options.sourceType || 'album'],
      success: (res) => {
        compressImageToBase64(res.tempFilePaths[0], options)
          .then(resolve)
          .catch(reject)
      }
    })
  })
}

// 图片压缩配置
export const IMAGE_PICKER_CONFIG = {
  avatar: {
    maxWidth: 400,
    maxHeight: 400,
    quality: 0.8
  }
}
```

### 4.3 时间验证工具
**实现位置**: `utils/avatarTimeValidator.js`

#### 头像更新时间限制
```javascript
export function canUpdateAvatar(lastAvatarTime) {
  if (!lastAvatarTime) {
    return { canUpdate: true, reason: '没有时间限制' }
  }
  
  const lastTime = new Date(lastAvatarTime)
  const currentTime = new Date()
  
  if (currentTime > lastTime) {
    return { canUpdate: true }
  } else {
    return { canUpdate: false, reason: '时间未到' }
  }
}
```

## 5. 微信小程序特有功能

### 5.1 二维码扫描处理
**实现位置**: 各激活页面

#### 场景值解析
```javascript
// 苗场激活 - scene参数解析
onLoad(options) {
  const scene = decodeURIComponent(options.scene || '')
  if (scene.startsWith('code=')) {
    this.activationCode = scene.substring(5)
  }
}

// 预播种 - no参数解析  
parseQrNo(options) {
  if (options.scene) {
    const scene = decodeURIComponent(options.scene)
    if (scene.startsWith('no=')) {
      return scene.substring(3)
    }
  }
  return options.no || null
}
```

### 5.2 微信原生功能集成
**实现位置**: `pages/avatar/index.vue`

#### 头像昵称更新
```javascript
// 使用微信小程序原生头像更新
<button open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
  <image :src="avatarUrl" mode="aspectFill"></image>
</button>

// 使用微信小程序原生昵称更新
<input type="nickname" @blur="onNicknameChange" 
       placeholder="请输入昵称" />
```

### 5.3 分享功能
**实现位置**: 二维码页面

#### 页面分享配置
```javascript
// 自定义分享内容
onShareAppMessage() {
  return {
    title: '预播种二维码',
    path: '/pages/PreSeed/verify/qrcode',
    imageUrl: this.qrCodeData?.wechatQrCode
  }
}
```

## 6. 性能优化策略

### 6.1 代码分包
**配置位置**: `manifest.json`

```json
{
  "mp-weixin": {
    "lazyCodeLoading": "requiredComponents",
    "optimization": {
      "subPackages": true
    }
  }
}
```

### 6.2 请求优化
- **缓存机制**: 60分钟认证缓存
- **请求去重**: 防止重复API调用
- **错误重试**: 网络错误自动重试机制

### 6.3 图片优化
- **压缩处理**: 自动压缩上传图片
- **格式转换**: 统一转换为base64格式
- **尺寸控制**: 根据用途设置不同压缩参数

## 7. 调试和开发工具

### 7.1 调试工具
**实现位置**: `utils/debug.js`, `utils/debugHelper.js`

#### 日志系统
```javascript
export const debug = {
  log: (module, message) => {
    console.log(`[${module}] ${message}`)
  },
  action: (module, action, message) => {
    console.log(`[${module}:${action}] ${message}`)
  },
  warn: (module, message) => {
    console.warn(`[${module}] ⚠️ ${message}`)
  }
}
```

### 7.2 错误处理策略
- **统一错误拦截**: request.js中的全局错误处理
- **用户友好提示**: 错误信息的用户化展示
- **错误恢复**: 认证失败自动重新登录

---

*本文档详细描述了项目的技术实现架构，为技术决策和代码维护提供指导*
