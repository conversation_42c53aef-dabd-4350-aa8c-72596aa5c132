/**
 * 页面事件监听 Mixin
 * 监听 App.vue 发出的认证事件，在认证完成后执行页面逻辑
 * 
 * 使用方法：
 * import pageEventMixin from '@/mixins/pageEvent.js'
 * 
 * export default {
 *   mixins: [pageEventMixin],
 *   
 *   // 认证完成回调
 *   onAuthReady(authData) {
 *     console.log('认证完成，可以执行页面逻辑')
 *   },
 *   
 *   // 认证失败回调
 *   onAuthFailed(error) {
 *     console.log('认证失败:', error)
 *   }
 * }
 */

import { mapGetters, mapState } from 'vuex'

export default {
  data() {
    return {
      // 认证相关状态
      authReady: false,        // 认证是否完成
      authSuccess: false,      // 认证是否成功
      authData: null,          // 认证数据
      authError: null,         // 认证错误信息
      pageOptions: null,       // 页面参数（在认证完成后处理）

      // 页面状态控制
      showPageContent: false,  // 是否显示页面内容
      pageLoading: true,       // 页面加载状态
      authWaitingMessage: '正在验证登录状态...'  // 等待提示信息
    }
  },

  computed: {
    ...mapGetters('auth', ['isTokenValid']),
    ...mapState('auth', ['hasLaunched']),

    // 是否需要等待认证完成
    needWaitAuth() {
      return !this.authReady || !this.authSuccess
    },

    // 是否可以执行页面业务逻辑
    canExecutePageLogic() {
      return this.authReady && this.authSuccess
    },

    // 是否可以发起网络请求
    canMakeNetworkRequest() {
      return this.authReady && this.authSuccess
    },

    // 页面状态文本
    pageStatusText() {
      if (!this.authReady) {
        return this.authWaitingMessage
      } else if (this.authError) {
        return '登录验证失败，请重试'
      } else if (!this.authSuccess) {
        return '登录验证失败'
      }
      return ''
    }
  },

  methods: {
    /**
     * 获取页面名称
     */
    getPageName() {
      // 优先使用组件的 name 属性
      if (this.$options.name) {
        // 将组件名称转换为友好的中文名称
        const nameMap = {
          'IndexPage': '首页',
          'ProfilePage': '个人中心',
          'AvatarPage': '编辑资料页面',
          'PreSeedLogPage': '试水苗页面',
          'PreseedLogsPage': '预种记录页面',
          'AgentActivePage': '代理激活页面',
          'MiaoChangActivePage': '苗场主激活页面'
        }
        return nameMap[this.$options.name] || this.$options.name
      }

      // 从路由信息获取
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        const route = currentPage.route || currentPage.__route__
        if (route) {
          // 提取页面名称，如 "pages/index/index" -> "首页"
          const pageNameMap = {
            'pages/index/index': '首页',
            'pages/profile/index': '个人中心',
            'pages/avatar/index': '编辑资料页面',
            'pages/PreSeed/PreSeedLog/index': '试水苗页面',
            'pages/PreSeed/logs/index': '预种记录页面',
            'pages/Agent/ActAgent/index': '代理激活页面',
            'pages/MiaoChang/ActMiaoChang/index': '苗场主激活页面'
          }
          return pageNameMap[route] || route.split('/').pop() || route
        }
      }

      // 从组件标签名获取
      if (this.$options._componentTag) {
        return this.$options._componentTag
      }

      return '未知页面'
    },

    /**
     * 等待认证完成
     */
    async waitForAuth() {
      const pageName = this.getPageName()
      console.log('⏳ 等待认证完成 -', pageName)

      // 设置等待状态
      this.pageLoading = true
      this.showPageContent = false
      this.authWaitingMessage = '正在验证登录状态...'

      // 检查 App.vue 的认证状态
      const authStatus = this.getAppAuthStatus()

      if (authStatus.isReady && authStatus.isLoggedIn) {
        console.log('✅ 认证已完成，直接执行页面逻辑')
        this.authReady = true
        this.authSuccess = true
        this.authData = {
          success: true,
          userInfo: authStatus.userInfo
        }

        // 显示页面内容
        this.showPageContent = true
        this.pageLoading = false

        // 立即执行认证完成回调
        if (typeof this.onAuthReady === 'function') {
          await this.onAuthReady(this.authData)
        }
        return
      }

      console.log('⏳ 认证未完成，等待认证事件...')
      // 如果认证未完成，事件监听器会处理
    },

    /**
     * 安全的网络请求方法
     * 只有在认证成功后才能发起请求
     */
    async safeNetworkRequest(requestFunction, ...args) {
      if (!this.canMakeNetworkRequest) {
        console.warn('🚫 认证未完成，无法发起网络请求')
        throw new Error('认证未完成，无法发起网络请求')
      }

      console.log('🌐 发起安全网络请求')
      return await requestFunction(...args)
    },

    /**
     * 安全的页面逻辑执行
     * 只有在认证成功后才能执行
     */
    async safeExecutePageLogic(logicFunction, ...args) {
      if (!this.canExecutePageLogic) {
        console.warn('🚫 认证未完成，无法执行页面逻辑')
        return false
      }

      console.log('⚡ 执行安全页面逻辑')
      return await logicFunction(...args)
    },

    /**
     * 获取 App 认证状态
     */
    getAppAuthStatus() {
      const pageName = this.getPageName()

      try {
        // 尝试从全局变量获取认证状态
        const app = getApp()
        if (app && app.globalData && typeof app.globalData.authStatusGetter === 'function') {
          console.log(`📱 ${pageName} - 使用全局变量获取认证状态`)
          return app.globalData.authStatusGetter()
        } else {
          console.log(`📱 ${pageName} - 全局认证状态方法不可用，使用降级方案`)
        }
      } catch (error) {
        console.warn(`${pageName} - 获取App认证状态失败，使用降级方案:`, error)
      }

      // 降级方案：直接检查 store
      console.log(`📱 ${pageName} - 使用Store直接检查认证状态`)
      return {
        isReady: this.hasLaunched,
        isLoggedIn: this.isTokenValid,
        userInfo: this.$store.state.user.userInfo
      }
    },

    /**
     * 处理认证完成事件
     */
    handleAuthReady(authData) {
      const pageName = this.getPageName()
      console.log(`🎉 ${pageName} - 收到认证完成事件:`, authData)

      // 检查认证是否真的成功
      if (authData && authData.success) {
        this.authReady = true
        this.authSuccess = true
        this.authData = authData
        this.authError = null

        // 显示页面内容
        this.showPageContent = true
        this.pageLoading = false

        console.log(`✅ ${pageName} - 认证成功，页面内容可以显示，网络请求可以发起`)

        // 执行页面的认证完成回调
        if (typeof this.onAuthReady === 'function') {
          this.onAuthReady(authData)
        }
      } else {
        console.warn(`⚠️ ${pageName} - 收到认证完成事件，但认证未成功`)
        this.handleAuthFailed({ error: '认证未成功' })
      }
    },

    /**
     * 处理认证失败事件
     */
    handleAuthFailed(errorData) {
      const pageName = this.getPageName()
      console.log(`❌ ${pageName} - 收到认证失败事件:`, errorData)

      this.authReady = true  // 认证流程完成，但失败
      this.authSuccess = false  // 认证失败
      this.authData = null
      this.authError = errorData.error || '认证失败'

      // 不显示页面内容，显示错误状态
      this.showPageContent = false
      this.pageLoading = false

      console.log(`🚫 ${pageName} - 认证失败，页面内容不可显示，网络请求不可发起`)

      // 执行页面的认证失败回调
      if (typeof this.onAuthFailed === 'function') {
        this.onAuthFailed(errorData)
      }
    }
  },

  /**
   * 页面加载时设置事件监听
   */
  onLoad(options) {
    const pageName = this.getPageName()
    console.log(`📱 ${pageName} - 页面加载，设置认证事件监听`)

    // 保存页面参数
    this.pageOptions = options

    // 监听认证事件
    uni.$on('auth-ready', this.handleAuthReady)
    uni.$on('auth-failed', this.handleAuthFailed)

    // 检查当前认证状态
    this.$nextTick(() => {
      this.waitForAuth()
    })
  },

  /**
   * 页面显示时检查认证状态
   */
  onShow() {
    const pageName = this.getPageName()

    // 如果认证未完成，重新等待
    if (!this.authReady) {
      console.log(`📱 ${pageName} - 页面显示，重新等待认证`)
      this.waitForAuth()
    }
  },

  /**
   * 页面卸载时移除事件监听
   */
  onUnload() {
    const pageName = this.getPageName()
    console.log(`${pageName} - 页面卸载，移除认证事件监听`)
    uni.$off('auth-ready', this.handleAuthReady)
    uni.$off('auth-failed', this.handleAuthFailed)
  }
}
