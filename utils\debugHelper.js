/**
 * 调试辅助工具
 * 用于分析和调试API响应格式问题
 */

/**
 * 深度分析对象结构
 * @param {any} obj 要分析的对象
 * @param {string} name 对象名称
 * @param {number} depth 当前深度
 * @param {number} maxDepth 最大深度
 */
export function analyzeObjectStructure(obj, name = 'object', depth = 0, maxDepth = 3) {
  const indent = '  '.repeat(depth)
  
  if (depth > maxDepth) {
    console.log(`${indent}[${name}]: [深度限制]`)
    return
  }
  
  if (obj === null) {
    console.log(`${indent}[${name}]: null`)
    return
  }
  
  if (obj === undefined) {
    console.log(`${indent}[${name}]: undefined`)
    return
  }
  
  const type = typeof obj
  
  if (type === 'object') {
    if (Array.isArray(obj)) {
      console.log(`${indent}[${name}]: Array(${obj.length})`)
      if (obj.length > 0) {
        analyzeObjectStructure(obj[0], `${name}[0]`, depth + 1, maxDepth)
      }
    } else {
      console.log(`${indent}[${name}]: Object`)
      const keys = Object.keys(obj)
      keys.forEach(key => {
        analyzeObjectStructure(obj[key], key, depth + 1, maxDepth)
      })
    }
  } else {
    const value = type === 'string' ? `"${obj}"` : obj
    console.log(`${indent}[${name}]: ${type} = ${value}`)
  }
}

/**
 * 分析API响应结构
 * @param {object} response API响应
 * @param {string} apiName API名称
 */
export function analyzeApiResponse(response, apiName = 'API') {
  console.log(`\n🔍 ===== ${apiName} 响应结构分析 =====`)
  console.log('📄 原始响应:', JSON.stringify(response, null, 2))
  console.log('\n📊 结构分析:')
  analyzeObjectStructure(response, 'response')
  console.log(`===== ${apiName} 分析结束 =====\n`)
}

/**
 * 比较两个对象的结构差异
 * @param {object} obj1 对象1
 * @param {object} obj2 对象2
 * @param {string} name1 对象1名称
 * @param {string} name2 对象2名称
 */
export function compareObjectStructures(obj1, obj2, name1 = 'object1', name2 = 'object2') {
  console.log(`\n🔍 ===== 对象结构比较: ${name1} vs ${name2} =====`)
  
  console.log(`\n📊 ${name1} 结构:`)
  analyzeObjectStructure(obj1, name1)
  
  console.log(`\n📊 ${name2} 结构:`)
  analyzeObjectStructure(obj2, name2)
  
  // 分析差异
  console.log('\n🔍 差异分析:')
  const keys1 = obj1 && typeof obj1 === 'object' ? Object.keys(obj1) : []
  const keys2 = obj2 && typeof obj2 === 'object' ? Object.keys(obj2) : []
  
  const onlyIn1 = keys1.filter(key => !keys2.includes(key))
  const onlyIn2 = keys2.filter(key => !keys1.includes(key))
  const common = keys1.filter(key => keys2.includes(key))
  
  if (onlyIn1.length > 0) {
    console.log(`❌ 只在 ${name1} 中存在:`, onlyIn1)
  }
  
  if (onlyIn2.length > 0) {
    console.log(`❌ 只在 ${name2} 中存在:`, onlyIn2)
  }
  
  if (common.length > 0) {
    console.log(`✅ 共同字段:`, common)
  }
  
  console.log(`===== 比较结束 =====\n`)
}

/**
 * 验证API响应格式
 * @param {object} response API响应
 * @param {object} expectedStructure 期望的结构
 * @param {string} apiName API名称
 */
export function validateApiResponseStructure(response, expectedStructure, apiName = 'API') {
  console.log(`\n🔍 ===== ${apiName} 响应格式验证 =====`)
  
  const issues = []
  
  function checkStructure(obj, expected, path = '') {
    if (expected === null || expected === undefined) {
      return
    }
    
    if (typeof expected === 'object' && !Array.isArray(expected)) {
      if (!obj || typeof obj !== 'object') {
        issues.push(`${path}: 期望对象，实际为 ${typeof obj}`)
        return
      }
      
      Object.keys(expected).forEach(key => {
        const newPath = path ? `${path}.${key}` : key
        if (!(key in obj)) {
          issues.push(`${newPath}: 缺少必需字段`)
        } else {
          checkStructure(obj[key], expected[key], newPath)
        }
      })
    } else if (Array.isArray(expected)) {
      if (!Array.isArray(obj)) {
        issues.push(`${path}: 期望数组，实际为 ${typeof obj}`)
      }
    } else if (typeof expected === 'string') {
      if (typeof obj !== expected) {
        issues.push(`${path}: 期望 ${expected} 类型，实际为 ${typeof obj}`)
      }
    }
  }
  
  checkStructure(response, expectedStructure)
  
  if (issues.length === 0) {
    console.log('✅ 响应格式验证通过')
  } else {
    console.log('❌ 响应格式验证失败:')
    issues.forEach(issue => console.log(`  - ${issue}`))
  }
  
  console.log(`===== 验证结束 =====\n`)
  
  return issues.length === 0
}

/**
 * JWT验证响应的期望结构
 */
export const JWT_VERIFY_EXPECTED_STRUCTURE = {
  code: 'number',
  msg: 'string',
  results: {
    // 格式1: jwtToken 嵌套
    jwtToken: {
      token: 'string',
      refresh: 'string'
    },
    user: {
      id: 'number',
      role: 'object'
    }
    // 格式2: 直接token字段
    // token: 'string',
    // refresh: 'string',
    // user: { ... }
  }
}

/**
 * 调试JWT验证响应
 * @param {object} response JWT验证API响应
 */
export function debugJwtVerifyResponse(response) {
  console.log('\n🔍 ===== JWT验证响应调试 =====')
  
  // 分析响应结构
  analyzeApiResponse(response, 'JWT验证')
  
  // 验证基本结构
  if (!response) {
    console.log('❌ 响应为空')
    return
  }
  
  if (response.code !== 200) {
    console.log(`❌ 响应码错误: ${response.code}, 消息: ${response.msg}`)
    return
  }
  
  const results = response.results
  if (!results) {
    console.log('❌ 缺少 results 字段')
    return
  }
  
  // 检查token格式
  console.log('\n🔍 Token格式检查:')
  if (results.jwtToken) {
    console.log('✅ 发现 jwtToken 嵌套格式')
    console.log('  - token:', results.jwtToken.token ? '存在' : '缺失')
    console.log('  - refresh:', results.jwtToken.refresh ? '存在' : '缺失')
  } else if (results.token) {
    console.log('✅ 发现直接 token 格式')
    console.log('  - token:', results.token ? '存在' : '缺失')
    console.log('  - refresh:', results.refresh ? '存在' : '缺失')
  } else {
    console.log('❌ 未发现任何 token 格式')
  }
  
  // 检查用户信息
  console.log('\n🔍 用户信息检查:')
  if (results.user) {
    console.log('✅ 发现用户信息')
    console.log('  - id:', results.user.id ? '存在' : '缺失')
    console.log('  - role:', results.user.role ? '存在' : '缺失')
    if (results.user.role) {
      console.log('  - role类型:', typeof results.user.role)
    }
  } else {
    console.log('❌ 缺少用户信息')
  }
  
  console.log('===== JWT验证调试结束 =====\n')
}
