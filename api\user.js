/**
 * 用户相关API接口
 *
 * 主要API端点: /api/user/update-avatar/ (POST)
 * 支持更新头像、昵称或两者
 *
 * API返回格式:
 * {
 *   code: 200,
 *   msg: '操作成功',
 *   results: {
 *     user: {
 *       id: number,
 *       nickname: string,
 *       avatar: string,
 *       employeePhone: string,
 *       openid: string,
 *       role: object | string
 *     }
 *   }
 * }
 */
import { request } from '../utils/request'
import { storageUtils, STORAGE_KEYS } from '../utils/storage'

/**
 * 获取认证头部
 */
function getAuthHeaders() {
  const authData = storageUtils.getItem(STORAGE_KEYS.AUTH_DATA, {})
  const token = authData.token
  
  if (!token) {
    throw new Error('未找到认证token')
  }
  
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
}

export const userAPI = {
  /**
   * 更新用户信息
   * @param {object} userInfo 用户信息
   * @param {string} userInfo.nickname 昵称
   * @param {string} userInfo.avatar 头像(base64格式)
   * @returns {Promise} 更新结果
   * @example
   * // API: POST /api/user/update-avatar/
   * // 请求格式:
   * {
   *   nickname: "新昵称",
   *   avatar: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
   * }
   *
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '更新成功',
   *   results: {
   *     user: { id: 1, nickname: '新昵称', avatar: 'http://...', ... }
   *   }
   * }
   */
  updateUserInfo(userInfo) {
    return request.post('/api/user/profile/update/', userInfo, {
      header: getAuthHeaders()
    })
  },

  /**
   * 上传头像 (base64格式)
   * @param {string} avatarBase64 头像base64数据
   * @returns {Promise} 上传结果
   * @example
   * // API: POST /api/user/update-avatar/
   * // 请求格式:
   * {
   *   avatar: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
   * }
   *
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '上传成功',
   *   results: {
   *     avatar_url: 'http://example.com/avatar.jpg',
   *     user: { id: 1, nickname: 'xxx', avatar: 'http://...', ... }
   *   }
   * }
   */
  uploadAvatar(avatarBase64) {
    return request.post('/api/user/update-avatar/', {
      avatar: avatarBase64
    }, {
      header: getAuthHeaders()
    })
  },

  /**
   * 更新昵称
   * @param {string} nickname 新昵称
   * @returns {Promise} 更新结果
   * @example
   * // API: POST /api/user/update-avatar/
   * // 请求格式:
   * {
   *   nickname: "新昵称"
   * }
   *
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '更新成功',
   *   results: {
   *     user: { id: 1, nickname: '新昵称', avatar: 'http://...', ... }
   *   }
   * }
   */
  updateNickname(nickname) {
    return request.post('/api/user/update-avatar/', {
      nickname: nickname
    }, {
      header: getAuthHeaders()
    })
  },

  /**
   * 获取用户详细信息
   * @returns {Promise} 用户信息
   * @example
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '获取成功',
   *   results: {
   *     user: { id: 1, nickname: 'xxx', avatar: 'http://...', ... }
   *   }
   * }
   */
  getUserInfo() {
    return request.get('/api/user/profile/', {}, {
      header: getAuthHeaders()
    })
  },

  /**
   * 批量更新用户信息 (昵称 + 头像)
   * @param {object} data 更新数据
   * @param {string} data.nickname 昵称
   * @param {string} data.avatar 头像base64
   * @returns {Promise} 更新结果
   * @example
   * // API: POST /api/user/update-avatar/
   * // 请求格式:
   * {
   *   nickname: "新昵称",
   *   avatar: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
   * }
   *
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '更新成功',
   *   results: {
   *     user: { id: 1, nickname: '新昵称', avatar: 'http://...', ... }
   *   }
   * }
   */
  batchUpdateProfile(data) {
    console.log('🔗 调用用户更新API:', '/api/user/update-avatar/')
    console.log('📤 请求数据:', {
      ...data,
      avatar: data.avatar ? `${data.avatar.substring(0, 50)}...` : undefined
    })

    return request.post('/api/user/update-avatar/', data, {
      header: getAuthHeaders()
    })
  },

  /**
   * 获取员工列表
   * @returns {Promise} 员工列表
   * @example
   * // API: GET /api/user/employee/list/
   * // 返回格式: 分页格式，与 preseed list 类似
   * {
   *   count: 5,
   *   next: null,
   *   previous: null,
   *   results: [
   *     {
   *       id: 1,
   *       nickname: '员工昵称',
   *       employeePhone: '13800138000',
   *       avatar: 'http://...',
   *       openid: 'xxx',
   *       role: {...}
   *     }
   *   ]
   * }
   */
  getEmployeeList() {
    console.log('调用员工列表API:', '/api/user/employee/list/')

    return request.get('/api/user/employee/list/', {}, {
      header: getAuthHeaders()
    })
  },

  /**
   * 更新员工信息
   * @param {object} employeeData 员工数据
   * @param {string} employeeData.nickname 昵称
   * @param {string} employeeData.employeePhone 电话
   * @param {string} employeeData.avatar 头像(base64格式)
   * @param {object} employeeData.permissions 权限对象，包含canXXX字段
   * @returns {Promise} 更新结果
   * @example
   * // API: POST /api/employee/update/
   * // 请求格式:
   * {
   *   id: 1,
   *   nickname: "员工昵称",
   *   employeePhone: "13800138000",
   *   avatar: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
   *   permissions: {
   *     canManageUsers: true,
   *     canViewReports: false,
   *     ...
   *   }
   * }
   */
  updateEmployee(employeeData) {
    console.log('🔗 调用员工更新API:', '/api/user/employee/update/')
    console.log('📤 请求数据:', {
      ...employeeData,
      avatar: employeeData.avatar ? `${employeeData.avatar.substring(0, 50)}...` : undefined
    })

    // 详细检查权限字段
    console.log('🔐 API请求中的权限字段:')
    const permissionFields = ['canCount', 'canMeasure', 'canFeed', 'canPreSeed', 'canSample', 'canDocs', 'canAccount']
    permissionFields.forEach(field => {
      console.log(`  - ${field}:`, employeeData[field], typeof employeeData[field])
    })

    return request.post('/api/user/employee/update/', employeeData, {
      header: getAuthHeaders()
    })
  },

  /**
   * 获取员工登录码
   * @param {number} employeeId 员工ID
   * @returns {Promise} 登录码结果
   * @example
   * // API: POST /api/user/employee/login/code/get/
   * // 请求格式:
   * {
   *   id: 1
   * }
   *
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '获取成功',
   *   results: {
   *     qrcodeUrl: 'http://example.com/qrcode.png'
   *   }
   * }
   */
  getEmployeeLoginCode(employeeId) {
    console.log('🔗 调用员工登录码API:', '/api/user/employee/login/code/get/')
    console.log('📤 请求数据:', { id: employeeId })

    return request.post('/api/user/employee/login/code/get/', {
      id: employeeId
    }, {
      header: getAuthHeaders()
    })
  }
}

/**
 * 用户信息更新响应适配器
 * @param {object} response API响应
 * @returns {object} 适配后的数据
 */
export function adaptUserUpdateResponse(response) {
  try {
    console.log('🔍 用户更新响应原始数据:', JSON.stringify(response, null, 2))
    
    if (!response || response.code !== 200) {
      throw new Error(response?.msg || '用户信息更新失败')
    }
    
    const results = response.results || {}
    const user = results.user
    
    if (!user) {
      throw new Error('用户更新响应格式错误：缺少用户信息')
    }
    
    console.log('✅ 用户信息更新成功')
    
    return {
      success: true,
      user: user,
      avatarUrl: results.avatar_url, // 如果是头像上传，可能包含新的头像URL
      message: response.msg || '更新成功'
    }
  } catch (error) {
    console.error('❌ 用户更新响应适配失败:', error.message)
    console.error('📄 原始响应:', JSON.stringify(response, null, 2))
    return {
      success: false,
      error: error.message,
      originalResponse: response
    }
  }
}

/**
 * 验证用户更新数据
 * @param {object} data 用户数据
 * @param {object} currentUserInfo 当前用户信息（用于时间验证）
 * @returns {boolean} 是否有效
 */
export function validateUserUpdateData(data, currentUserInfo = null) {
  if (!data || typeof data !== 'object') {
    return false
  }

  console.log('🔍 验证用户更新数据:')
  console.log('  - 更新数据:', data)
  console.log('  - 当前用户信息:', currentUserInfo)

  // 检查昵称
  if (data.nickname !== undefined) {
    if (typeof data.nickname !== 'string' || data.nickname.trim().length === 0) {
      throw new Error('昵称不能为空')
    }
    if (data.nickname.length > 20) {
      throw new Error('昵称长度不能超过20个字符')
    }
    console.log('✅ 昵称验证通过')
  }

  // 检查头像base64格式
  if (data.avatar !== undefined) {
    if (typeof data.avatar !== 'string') {
      throw new Error('头像数据格式错误')
    }
    if (!data.avatar.startsWith('data:image/')) {
      throw new Error('头像必须是base64格式的图片')
    }
    console.log('✅ 头像格式验证通过')
  }

  // 时间验证：检查 last_avatar_time 是否大于当前时间
  if ((data.avatar !== undefined || data.nickname !== undefined) && currentUserInfo) {
    console.log('🕐 开始时间验证...')

    try {
      const lastAvatarTime = currentUserInfo.last_avatar_time || currentUserInfo.lastAvatarTime

      if (lastAvatarTime) {
        console.log('📅 检查 last_avatar_time:', lastAvatarTime)

        // 解析时间字符串 (格式: 2025-08-10 16:03:56)
        const lastTime = new Date(lastAvatarTime)
        const currentTime = new Date()

        console.log('⏰ 上次更新时间:', lastTime.toISOString())
        console.log('⏰ 当前时间:', currentTime.toISOString())

        // 检查 last_avatar_time 是否大于当前时间
        if (lastTime.getTime() > currentTime.getTime()) {
          const timeDiff = lastTime.getTime() - currentTime.getTime()
          const remainingMinutes = Math.ceil(timeDiff / (1000 * 60))

          console.error('❌ 时间验证失败: last_avatar_time 大于当前时间')
          console.error('   还需等待:', remainingMinutes, '分钟')

          const errorMessage = `更新过于频繁，还需等待 ${remainingMinutes} 分钟`
          throw new Error(errorMessage)
        }

        console.log('✅ 时间验证通过: 当前时间大于 last_avatar_time')
      } else {
        console.log('✅ 没有 last_avatar_time，允许更新')
      }
    } catch (error) {
      console.error('❌ 时间验证出错:', error)
      // 如果是我们抛出的验证错误，继续抛出
      if (error.message.includes('更新过于频繁')) {
        throw error
      }
      // 其他错误（如时间解析错误）不阻塞用户，继续执行
      console.log('⚠️ 时间验证出错，默认允许更新')
    }
  }

  console.log('✅ 所有验证通过')
  return true
}

export default userAPI
