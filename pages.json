{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "苗邦"
			}
		},
		{
			"path": "pages/PreSeed/PreSeedLog/index",
			"style": {
				"navigationBarTitleText": "试水苗领取"
			}
		},
		{
			"path": "pages/PreSeed/boss/generate/qrcode",
			"style": {
				"navigationBarTitleText": "试水苗二维码"
			}
		},
		{
			"path": "pages/PreSeed/verify/qrcode",
			"style": {
				"navigationBarTitleText": "试水苗验证"
			}
		},
		{
			"path": "pages/Agent/ActAgent/index",
			"style": {
				"navigationBarTitleText": "成为代理"
			}
		},
		{
			"path": "pages/MiaoChang/ActMiaoChang/index",
			"style": {
				"navigationBarTitleText": "激活苗场主"
			}
		},
		{
			"path": "pages/profile/index",
			"style": {
				"navigationBarTitleText": "个人中心"
			}
		},
		{
			"path": "pages/avatar/index",
			"style": {
				"navigationBarTitleText": "编辑资料"
			}
		},
		{
			"path": "pages/PreSeed/logs/index",
			"style": {
				"navigationBarTitleText": "试水苗记录"
			}
		},
		{
			"path": "pages/invite/agent/inviteagentpage",
			"style": {
				"navigationBarTitleText": "生成中介邀请码"
			}
		},
		{
			"path": "pages/Agent/list/agentlist",
			"style": {
				"navigationBarTitleText": "代理列表",
				"enablePullDownRefresh": true,
				"onReachBottomDistance": 50
			}
		},
		{
			"path": "pages/employee/list/index",
			"style": {
				"navigationBarTitleText": "员工列表"
			}
		},
		{
			"path": "pages/employee/edit/index",
			"style": {
				"navigationBarTitleText": "编辑员工"
			}
		},
		{
			"path": "pages/employee/logincode/index",
			"style": {
				"navigationBarTitleText": "员工登录码"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "#007aff",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/index/index",
				"iconPath": "static/tabbar/home.png",
				"selectedIconPath": "static/tabbar/home-active.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/profile/index",
				"iconPath": "static/tabbar/profile.png",
				"selectedIconPath": "static/tabbar/profile-active.png",
				"text": "我的"
			}
		]
	},
	"uniIdRouter": {}
}
