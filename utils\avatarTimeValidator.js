/**
 * 头像更新时间验证工具
 * 简化版本：只检查 last_avatar_time 是否大于当前时间
 */

/**
 * 检查是否可以更新头像/昵称
 * @param {string} lastAvatarTime 上次更新时间 (格式: 2025-08-10 16:03:56)
 * @returns {object} 验证结果
 */
export function canUpdateAvatar(lastAvatarTime) {
  console.log('🕐 检查头像更新时间限制:')
  console.log('  - last_avatar_time:', lastAvatarTime)
  
  try {
    // 如果没有上次更新时间，允许更新
    if (!lastAvatarTime) {
      console.log('✅ 没有 last_avatar_time，允许更新')
      return {
        canUpdate: true,
        reason: '没有时间限制'
      }
    }
    
    // 解析时间字符串 (格式: 2025-08-10 16:03:56)
    const lastTime = new Date(lastAvatarTime)
    const currentTime = new Date()
    
    // 检查时间是否有效
    if (isNaN(lastTime.getTime())) {
      console.warn('⚠️ last_avatar_time 格式无效:', lastAvatarTime)
      return {
        canUpdate: true,
        reason: '时间格式无效，默认允许'
      }
    }
    
    console.log('📅 时间对比:')
    console.log('  - 上次更新时间:', lastTime.toLocaleString())
    console.log('  - 当前时间:', currentTime.toLocaleString())
    console.log('  - last_avatar_time ISO:', lastTime.toISOString())
    console.log('  - 当前时间 ISO:', currentTime.toISOString())
    
    // 检查 last_avatar_time 是否大于当前时间
    if (lastTime.getTime() > currentTime.getTime()) {
      const timeDiff = lastTime.getTime() - currentTime.getTime()
      const remainingMinutes = Math.ceil(timeDiff / (1000 * 60))
      const remainingSeconds = Math.ceil(timeDiff / 1000)
      
      console.log('❌ 时间验证失败: last_avatar_time 大于当前时间')
      console.log('  - 时间差:', timeDiff, '毫秒')
      console.log('  - 还需等待:', remainingMinutes, '分钟')
      console.log('  - 还需等待:', remainingSeconds, '秒')
      
      return {
        canUpdate: false,
        reason: 'last_avatar_time 大于当前时间',
        remainingMinutes: remainingMinutes,
        remainingSeconds: remainingSeconds,
        lastTime: lastTime,
        currentTime: currentTime
      }
    } else {
      console.log('✅ 时间验证通过: 当前时间大于 last_avatar_time')
      return {
        canUpdate: true,
        reason: '当前时间大于 last_avatar_time',
        lastTime: lastTime,
        currentTime: currentTime
      }
    }
    
  } catch (error) {
    console.error('❌ 时间验证出错:', error)
    // 出错时默认允许更新，避免阻塞用户
    return {
      canUpdate: true,
      reason: '时间验证出错，默认允许',
      error: error.message
    }
  }
}

/**
 * 格式化剩余时间为友好的文本
 * @param {number} minutes 剩余分钟数
 * @returns {string} 格式化的时间文本
 */
export function formatRemainingTime(minutes) {
  if (minutes <= 0) {
    return '现在可以更新'
  }
  
  if (minutes < 60) {
    return `还需等待 ${minutes} 分钟`
  }
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  
  if (remainingMinutes === 0) {
    return `还需等待 ${hours} 小时`
  }
  
  return `还需等待 ${hours} 小时 ${remainingMinutes} 分钟`
}

/**
 * 检查用户信息中的头像更新时间限制
 * @param {object} userInfo 用户信息
 * @returns {object} 验证结果
 */
export function checkUserAvatarTime(userInfo) {
  console.log('👤 检查用户头像更新时间限制:')
  console.log('  - userInfo:', userInfo)
  
  if (!userInfo || typeof userInfo !== 'object') {
    console.warn('⚠️ 用户信息无效')
    return {
      canUpdate: true,
      reason: '用户信息无效，默认允许'
    }
  }
  
  // 检查 last_avatar_time 字段
  const lastAvatarTime = userInfo.last_avatar_time || userInfo.lastAvatarTime
  
  return canUpdateAvatar(lastAvatarTime)
}

/**
 * 验证时间字符串格式
 * @param {string} timeString 时间字符串
 * @returns {boolean} 是否为有效格式
 */
export function isValidTimeFormat(timeString) {
  if (!timeString || typeof timeString !== 'string') {
    return false
  }
  
  // 检查格式: 2025-08-10 16:03:56
  const timeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/
  if (!timeRegex.test(timeString)) {
    return false
  }
  
  // 检查是否可以正确解析
  const date = new Date(timeString)
  return !isNaN(date.getTime())
}

/**
 * 格式化当前时间为指定格式
 * @returns {string} 格式化的时间字符串 (2025-08-10 16:03:56)
 */
export function getCurrentTimeString() {
  const now = new Date()
  
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 解析时间字符串为Date对象
 * @param {string} timeString 时间字符串 (2025-08-10 16:03:56)
 * @returns {Date} Date对象
 */
export function parseTimeString(timeString) {
  if (!timeString) {
    throw new Error('时间字符串不能为空')
  }
  
  const date = new Date(timeString)
  
  if (isNaN(date.getTime())) {
    throw new Error(`无效的时间格式: ${timeString}`)
  }
  
  return date
}

/**
 * 比较两个时间字符串
 * @param {string} time1 时间1
 * @param {string} time2 时间2
 * @returns {number} 比较结果 (-1: time1 < time2, 0: 相等, 1: time1 > time2)
 */
export function compareTimeStrings(time1, time2) {
  const date1 = parseTimeString(time1)
  const date2 = parseTimeString(time2)
  
  if (date1.getTime() < date2.getTime()) {
    return -1
  } else if (date1.getTime() > date2.getTime()) {
    return 1
  } else {
    return 0
  }
}

export default {
  canUpdateAvatar,
  formatRemainingTime,
  checkUserAvatarTime,
  isValidTimeFormat,
  getCurrentTimeString,
  parseTimeString,
  compareTimeStrings
}
