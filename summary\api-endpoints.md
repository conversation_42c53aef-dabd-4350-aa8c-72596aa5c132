# API接口文档

## 1. 认证相关接口

### 1.1 微信登录
```http
POST /api/wechat/login/
```

**请求参数**:
```json
{
  "code": "微信登录code"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "results": {
    "jwtToken": {
      "token": "JWT_ACCESS_TOKEN",
      "refresh": "JWT_REFRESH_TOKEN"
    },
    "user": {
      "id": 1,
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "phone": "手机号",
      "openid": "微信openid",
      "role": {
        "id": 1,
        "name": "角色名称",
        "code": 8888
      }
    }
  }
}
```

### 1.2 JWT验证
```http
POST /api/auth/jwt/verify/
```

**请求头**:
```http
Authorization: Bearer {JWT_TOKEN}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "验证成功",
  "results": {
    "jwtToken": {
      "token": "NEW_JWT_TOKEN",
      "refresh": "NEW_REFRESH_TOKEN"
    },
    "user": {
      "id": 1,
      "nickname": "用户昵称",
      "role": {...}
    }
  }
}
```

## 2. 用户相关接口

### 2.1 更新用户信息
```http
POST /api/user/profile/update/
```

**请求头**:
```http
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatar": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "更新成功",
  "results": {
    "user": {
      "id": 1,
      "nickname": "新昵称",
      "avatar": "新头像URL",
      "last_avatar_time": "2025-08-10 16:03:56",
      "role": {...}
    }
  }
}
```

## 3. 员工管理接口

### 3.1 员工列表
```http
GET /api/user/employee/list/
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `page_size`: 每页数量 (默认: 20)

**响应格式**:
```json
{
  "count": 100,
  "next": "下一页URL",
  "previous": "上一页URL", 
  "results": [
    {
      "id": 1,
      "nickname": "员工昵称",
      "avatar": "头像URL",
      "phone": "手机号",
      "role": {
        "id": 2,
        "name": "员工",
        "code": 2222
      },
      "canCount": true,
      "canMeasure": false,
      "canFeed": true,
      "canPreSeed": false,
      "canSample": true,
      "canDocs": false,
      "canAccount": false,
      "miaochang": {
        "id": 1,
        "name": "苗场名称",
        "company": "公司名称"
      }
    }
  ]
}
```

### 3.2 更新员工信息
```http
POST /api/employee/update/
```

**请求参数**:
```json
{
  "id": 1,
  "nickname": "新昵称",
  "phone": "新手机号",
  "avatar": "base64头像",
  "canCount": true,
  "canMeasure": false,
  "canFeed": true,
  "canPreSeed": false,
  "canSample": true,
  "canDocs": false,
  "canAccount": false
}
```

### 3.3 员工登录码
```http
GET /api/user/employee/login/code/get/
```

**查询参数**:
- `employee_id`: 员工ID

**响应格式**:
```json
{
  "code": 200,
  "msg": "生成成功",
  "results": {
    "qrcodeUrl": "二维码图片URL"
  }
}
```

## 4. 代理商管理接口

### 4.1 代理商列表
```http
GET /api/miaochang/agent/list/
```

**查询参数**:
- `page`: 页码
- `page_size`: 每页数量
- `search`: 搜索关键词
- `status`: 状态筛选

**响应格式**:
```json
{
  "count": 50,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "status": 1,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "miaochang": {
        "id": 1,
        "name": "苗场名称",
        "company": "公司名称"
      },
      "agent": {
        "id": 2,
        "nickname": "代理商昵称",
        "avatar": "头像URL",
        "phone": "手机号"
      },
      "remark": "备注信息"
    }
  ]
}
```

### 4.2 更新代理商
```http
POST /api/miaochang/agent/update/
```

**请求参数**:
```json
{
  "id": 1,
  "nickname": "新昵称",
  "phone": "新手机号",
  "remark": "新备注"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "更新成功",
  "results": {
    "agent": {...}
  }
}
```

### 4.3 禁用/启用代理商
```http
GET /api/miaochang/agent/disable/{id}
```

**响应格式**:
```json
{
  "id": 1,
  "status": 0,
  "agent": {...}
}
```

## 5. 预播种相关接口

### 5.1 生成预播种二维码
```http
GET /api/wechat/preseed/boss/qrcode/get/
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "生成成功",
  "results": {
    "wechatQrCode": "二维码图片URL",
    "miaochang": {
      "name": "苗场名称"
    }
  }
}
```

### 5.2 验证预播种二维码
```http
POST /api/wechat/preseed/qrcode/verify/
```

**请求参数**:
```json
{
  "no": "预播种编号"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "验证成功",
  "results": {
    "miaochang": {
      "name": "苗场名称"
    },
    "quantity": 100
  }
}
```

### 5.3 确认预播种
```http
POST /api/wechat/preseed/qrcode/confirm/
```

**请求参数**:
```json
{
  "id": 1
}
```

### 5.4 预播种记录
```http
GET /api/preseed/lists/
```

**查询参数**:
- `page`: 页码
- `page_size`: 每页数量

**响应格式**:
```json
{
  "count": 30,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "miaochang": {
        "name": "苗场名称"
      },
      "user": {
        "avatar": "用户头像"
      },
      "preSeedPerson": {
        "avatar": "操作人头像"
      },
      "created_at": "2024-01-01T00:00:00Z",
      "quantity": 50
    }
  ]
}
```

## 6. 苗场激活接口

### 6.1 获取激活码信息
```http
GET /api/wechat/miaochang/activate/get/
```

**查询参数**:
- `code`: 激活码

**响应格式**:
```json
{
  "code": 200,
  "msg": "获取成功",
  "results": {
    "id": 1,
    "name": "苗场名称",
    "phone": "联系电话",
    "miniQrCode": "小程序二维码URL"
  }
}
```

### 6.2 确认苗场激活
```http
POST /api/wechat/miaochang/activate/confirm/
```

**请求参数**:
```json
{
  "id": 1
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "激活成功",
  "results": {
    "jwtToken": {
      "token": "NEW_JWT_TOKEN",
      "refresh": "NEW_REFRESH_TOKEN"
    },
    "user": {
      "id": 1,
      "nickname": "用户昵称",
      "role": {
        "code": 5555,
        "name": "苗场主"
      }
    }
  }
}
```

## 7. 代理商激活接口

### 7.1 验证代理码
```http
POST /api/wechat/act/agent/
```

**请求参数**:
```json
{
  "code": "代理邀请码"
}
```

### 7.2 确认代理激活
```http
POST /api/wechat/agent/confirm/
```

**请求参数**:
```json
{
  "agentInfo": {
    "id": 1,
    "name": "代理商名称"
  }
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "激活成功",
  "results": {
    "user": {...}
  }
}
```

## 8. 通用响应格式

### 8.1 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "results": {
    // 具体数据
  }
}
```

### 8.2 错误响应
```json
{
  "code": 400,
  "msg": "错误信息",
  "results": null
}
```

### 8.3 认证错误
```json
{
  "code": 401,
  "msg": "未授权访问",
  "results": null
}
```

---

*本文档涵盖了项目中所有主要API接口的详细说明，为前后端对接提供参考*
