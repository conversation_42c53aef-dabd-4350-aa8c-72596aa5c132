/**
 * 调试工具
 * 用于开发环境的调试和日志输出
 */
import config from '../config/env'

export const debug = {
  /**
   * 调试日志输出
   * @param {string} tag 标签
   * @param {any} data 数据
   */
  log(tag, ...data) {
    if (config.DEBUG) {
      console.log(`[${tag}]`, ...data)
    }
  },
  
  /**
   * 错误日志输出
   * @param {string} tag 标签
   * @param {any} error 错误信息
   */
  error(tag, ...error) {
    if (config.DEBUG) {
      console.error(`[${tag}]`, ...error)
    }
  },
  
  /**
   * 警告日志输出
   * @param {string} tag 标签
   * @param {any} warning 警告信息
   */
  warn(tag, ...warning) {
    if (config.DEBUG) {
      console.warn(`[${tag}]`, ...warning)
    }
  },
  
  /**
   * 网络请求日志
   * @param {string} method 请求方法
   * @param {string} url 请求URL
   * @param {any} data 请求数据
   */
  request(method, url, data) {
    if (config.DEBUG) {
      console.group(`🌐 ${method.toUpperCase()} ${url}`)
      console.log('请求数据:', data)
      console.groupEnd()
    }
  },
  
  /**
   * 网络响应日志
   * @param {string} method 请求方法
   * @param {string} url 请求URL
   * @param {any} response 响应数据
   */
  response(method, url, response) {
    if (config.DEBUG) {
      console.group(`📡 ${method.toUpperCase()} ${url} - ${response.statusCode || 'Unknown'}`)
      console.log('响应数据:', response.data || response)
      console.groupEnd()
    }
  },
  
  /**
   * 状态变更日志
   * @param {string} module 模块名
   * @param {string} mutation mutation名称
   * @param {any} payload 载荷
   */
  mutation(module, mutation, payload) {
    if (config.DEBUG) {
      console.log(`🔄 [${module}] ${mutation}`, payload)
    }
  },
  
  /**
   * Action执行日志
   * @param {string} module 模块名
   * @param {string} action action名称
   * @param {any} payload 载荷
   */
  action(module, action, payload) {
    if (config.DEBUG) {
      console.log(`⚡ [${module}] ${action}`, payload)
    }
  },
  
  /**
   * 用户操作日志
   * @param {string} event 事件名称
   * @param {any} data 事件数据
   */
  userEvent(event, data) {
    if (config.DEBUG) {
      console.log(`👆 用户操作: ${event}`, data)
    }
  },
  
  /**
   * 性能计时开始
   * @param {string} label 标签
   */
  timeStart(label) {
    if (config.DEBUG) {
      console.time(label)
    }
  },
  
  /**
   * 性能计时结束
   * @param {string} label 标签
   */
  timeEnd(label) {
    if (config.DEBUG) {
      console.timeEnd(label)
    }
  }
}
