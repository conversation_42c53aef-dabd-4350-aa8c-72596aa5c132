/**
 * 预种相关API
 */
import { request } from '../utils/request'

/**
 * 预种API接口
 */
const preseedAPI = {
  /**
   * 获取预种记录列表
   * @param {Object} params 查询参数
   * @param {number} params.page 页码，默认1
   * @param {number} params.page_size 每页数量，默认20
   * @returns {Promise} API响应
   */
  async getPreseedLogs(params = {}) {
    const defaultParams = {
      page: 1,
      page_size: 20
    }

    const queryParams = { ...defaultParams, ...params }

    console.log('获取预种记录列表，参数:', queryParams)
    
    try {
      const response = await request.get('/api/preseed/lists/', queryParams)
      
      console.log('预种记录API响应:', response)
      
      // API返回直接分页格式，不是{code, msg, results}包装
      if (response && typeof response === 'object') {
        return response
      } else {
        throw new Error('API返回格式错误')
      }
    } catch (error) {
      console.error('❌ 获取预种记录失败:', error)
      throw error
    }
  }
}

export default preseedAPI
