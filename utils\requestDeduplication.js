/**
 * 请求去重工具
 * 防止短时间内的重复请求
 */

class RequestDeduplication {
  constructor() {
    this.pendingRequests = new Map()
    this.requestHistory = new Map()
  }
  
  /**
   * 生成请求唯一标识
   * @param {string} url 请求URL
   * @param {string} method 请求方法
   * @param {object} data 请求数据
   * @returns {string} 请求标识
   */
  generateRequestKey(url, method, data) {
    const dataStr = JSON.stringify(data || {})
    return `${method.toUpperCase()}_${url}_${dataStr}`
  }
  
  /**
   * 检查是否为重复请求
   * @param {string} url 请求URL
   * @param {string} method 请求方法
   * @param {object} data 请求数据
   * @param {number} deduplicationTime 去重时间窗口（毫秒）
   * @returns {boolean} 是否为重复请求
   */
  isDuplicateRequest(url, method, data, deduplicationTime = 1000) {
    const requestKey = this.generateRequestKey(url, method, data)
    const now = Date.now()
    
    // 检查是否有正在进行的相同请求
    if (this.pendingRequests.has(requestKey)) {
      console.warn('🚫 检测到重复请求（正在进行中）:', requestKey)
      return true
    }
    
    // 检查历史请求记录
    const lastRequestTime = this.requestHistory.get(requestKey)
    if (lastRequestTime && (now - lastRequestTime) < deduplicationTime) {
      console.warn('🚫 检测到重复请求（时间窗口内）:', requestKey, '距离上次请求:', now - lastRequestTime, 'ms')
      return true
    }
    
    return false
  }
  
  /**
   * 标记请求开始
   * @param {string} url 请求URL
   * @param {string} method 请求方法
   * @param {object} data 请求数据
   */
  markRequestStart(url, method, data) {
    const requestKey = this.generateRequestKey(url, method, data)
    this.pendingRequests.set(requestKey, Date.now())
    console.log('🟡 请求开始:', requestKey)
  }
  
  /**
   * 标记请求结束
   * @param {string} url 请求URL
   * @param {string} method 请求方法
   * @param {object} data 请求数据
   */
  markRequestEnd(url, method, data) {
    const requestKey = this.generateRequestKey(url, method, data)
    const now = Date.now()
    
    // 移除正在进行的请求记录
    this.pendingRequests.delete(requestKey)
    
    // 记录到历史请求
    this.requestHistory.set(requestKey, now)
    
    console.log('🟢 请求结束:', requestKey)
    
    // 清理过期的历史记录（保留最近5分钟的记录）
    this.cleanupHistory(5 * 60 * 1000)
  }
  
  /**
   * 清理过期的历史记录
   * @param {number} maxAge 最大保留时间（毫秒）
   */
  cleanupHistory(maxAge = 5 * 60 * 1000) {
    const now = Date.now()
    const expiredKeys = []
    
    for (const [key, timestamp] of this.requestHistory.entries()) {
      if (now - timestamp > maxAge) {
        expiredKeys.push(key)
      }
    }
    
    expiredKeys.forEach(key => {
      this.requestHistory.delete(key)
    })
    
    if (expiredKeys.length > 0) {
      console.log('🧹 清理过期请求记录:', expiredKeys.length, '条')
    }
  }
  
  /**
   * 获取当前状态
   * @returns {object} 当前状态信息
   */
  getStatus() {
    return {
      pendingRequests: this.pendingRequests.size,
      historyRequests: this.requestHistory.size,
      pendingKeys: Array.from(this.pendingRequests.keys()),
      historyKeys: Array.from(this.requestHistory.keys())
    }
  }
  
  /**
   * 清空所有记录
   */
  clear() {
    this.pendingRequests.clear()
    this.requestHistory.clear()
    console.log('🧹 清空所有请求记录')
  }
}

// 创建全局实例
export const requestDeduplication = new RequestDeduplication()

// 导出类供其他地方使用
export default RequestDeduplication
