/**
 * HTTP请求拦截器
 * 统一处理token添加、错误处理等
 */
import config from '../config/env'
import { requestDeduplication } from './requestDeduplication'

// 基础配置
const BASE_URL = config.BASE_URL
const TIMEOUT = config.TIMEOUT || 10000

// 获取store实例的函数
function getStore() {
  try {
    // 尝试获取store实例
    const app = getApp()
    if (app && app.$store) {
      return app.$store
    }

    // 备用方案：通过全局变量获取
    if (typeof getCurrentPages === 'function') {
      const pages = getCurrentPages()
      if (pages.length > 0 && pages[0].$store) {
        return pages[0].$store
      }
    }

    return null
  } catch (error) {
    console.warn('获取store失败:', error)
    return null
  }
}

// 请求拦截器
uni.addInterceptor('request', {
  invoke(args) {
    // 添加基础URL
    if (!args.url.startsWith('http')) {
      args.url = BASE_URL + args.url
    }

    // 添加默认header
    args.header = {
      'Content-Type': 'application/json',
      ...args.header
    }

    // 添加token到请求头
    try {
      let token = null

      // 优先从store获取token
      const store = getStore()
      if (store && store.state && store.state.auth && store.state.auth.token) {
        token = store.state.auth.token
        console.log('✅ 从store获取token')
      } else {
        // 降级方案：从storage获取
        const authData = uni.getStorageSync('auth_data')
        if (authData && authData.token) {
          token = authData.token
          console.log('⚠️ 从storage获取token (降级方案)')
        }
      }

      if (token) {
        args.header['Authorization'] = `Bearer ${token}`
        console.log('🔐 已添加Authorization头')
      } else {
        console.warn('⚠️ 未找到有效token')
      }
    } catch (error) {
      console.error('❌ 获取token失败:', error)
    }

    console.log('发起请求:', args.url, args.method || 'GET')
    return args
  },
  
  success(res) {
    console.log('请求成功:', res.statusCode)

    // 处理token过期或未授权
    if (res.statusCode === 401) {
      console.error('❌ 401 Unauthorized - Token过期或无效')

      // 清除本地认证信息
      try {
        uni.removeStorageSync('auth_data')

        // 尝试清除store中的认证信息
        const store = getStore()
        if (store && store.dispatch) {
          store.dispatch('auth/clearAuth').catch(err => {
            console.warn('清除store认证信息失败:', err)
          })
        }
      } catch (error) {
        console.error('清除认证信息失败:', error)
      }

      // 显示错误提示
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none',
        duration: 3000
      })

      // 延迟跳转到首页，触发重新登录
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/index/index'
        })
      }, 2000)
    }

    return res
  },
  
  fail(err) {
    console.error('请求失败:', err)
    
    // 网络错误处理
    let errorMessage = '网络请求失败'
    
    if (err.errMsg) {
      if (err.errMsg.includes('timeout')) {
        errorMessage = '请求超时，请检查网络连接'
      } else if (err.errMsg.includes('fail')) {
        errorMessage = '网络连接失败，请检查网络设置'
      }
    }
    
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    })
    
    return err
  }
})

/**
 * 封装的请求方法
 */
export const request = {
  /**
   * GET请求
   */
  get(url, data = {}, options = {}) {
    return new Promise((resolve, reject) => {
      uni.request({
        url,
        method: 'GET',
        data,
        timeout: TIMEOUT,
        ...options,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: reject
      })
    })
  },
  
  /**
   * POST请求
   */
  post(url, data = {}, options = {}) {
    return new Promise((resolve, reject) => {
      // 对登录相关接口进行去重检查
      const isLoginRequest = url.includes('/login/') || url.includes('/verify/')
      if (isLoginRequest && requestDeduplication.isDuplicateRequest(url, 'POST', data, 2000)) {
        reject(new Error('重复请求被拦截'))
        return
      }

      // 标记请求开始
      if (isLoginRequest) {
        requestDeduplication.markRequestStart(url, 'POST', data)
      }

      uni.request({
        url,
        method: 'POST',
        data,
        timeout: TIMEOUT,
        ...options,
        success: (res) => {
          // 标记请求结束
          if (isLoginRequest) {
            requestDeduplication.markRequestEnd(url, 'POST', data)
          }

          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: (err) => {
          // 标记请求结束
          if (isLoginRequest) {
            requestDeduplication.markRequestEnd(url, 'POST', data)
          }
          reject(err)
        }
      })
    })
  },
  
  /**
   * PUT请求
   */
  put(url, data = {}, options = {}) {
    return new Promise((resolve, reject) => {
      uni.request({
        url,
        method: 'PUT',
        data,
        ...options,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: reject
      })
    })
  },

  /**
   * PATCH请求
   */
  patch(url, data = {}, options = {}) {
    return new Promise((resolve, reject) => {
      uni.request({
        url,
        method: 'POST', // 小程序可能不支持PATCH，使用POST代替
        data: {
          _method: 'PATCH', // 添加方法标识
          ...data
        },
        ...options,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: reject
      })
    })
  },
  
  /**
   * DELETE请求
   */
  delete(url, data = {}, options = {}) {
    return new Promise((resolve, reject) => {
      uni.request({
        url,
        method: 'DELETE',
        data,
        ...options,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: reject
      })
    })
  }
}
