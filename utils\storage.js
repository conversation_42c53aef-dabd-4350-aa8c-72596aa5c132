/**
 * 本地存储工具类
 * 封装uni.storage API，提供统一的存储接口
 */
export const storageUtils = {
  /**
   * 设置存储数据
   * @param {string} key 存储键名
   * @param {any} value 存储值
   * @returns {boolean} 是否成功
   */
  setItem(key, value) {
    try {
      uni.setStorageSync(key, value)
      console.log(`存储数据成功: ${key}`)
      return true
    } catch (error) {
      console.error('存储数据失败:', error)
      return false
    }
  },
  
  /**
   * 获取存储数据
   * @param {string} key 存储键名
   * @param {any} defaultValue 默认值
   * @returns {any} 存储的值或默认值
   */
  getItem(key, defaultValue = null) {
    try {
      const value = uni.getStorageSync(key)
      return value !== '' ? value : defaultValue
    } catch (error) {
      console.error('获取数据失败:', error)
      return defaultValue
    }
  },
  
  /**
   * 删除存储数据
   * @param {string} key 存储键名
   * @returns {boolean} 是否成功
   */
  removeItem(key) {
    try {
      uni.removeStorageSync(key)
      console.log(`删除数据成功: ${key}`)
      return true
    } catch (error) {
      console.error('删除数据失败:', error)
      return false
    }
  },
  
  /**
   * 清空所有存储
   * @returns {boolean} 是否成功
   */
  clear() {
    try {
      uni.clearStorageSync()
      console.log('清空存储成功')
      return true
    } catch (error) {
      console.error('清空存储失败:', error)
      return false
    }
  },
  
  /**
   * 检查键是否存在
   * @param {string} key 存储键名
   * @returns {boolean} 是否存在
   */
  hasKey(key) {
    try {
      const value = uni.getStorageSync(key)
      return value !== ''
    } catch (error) {
      console.error('检查键存在失败:', error)
      return false
    }
  },
  
  /**
   * 获取存储信息
   * @returns {object} 存储信息
   */
  getStorageInfo() {
    try {
      return uni.getStorageInfoSync()
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return {
        keys: [],
        currentSize: 0,
        limitSize: 0
      }
    }
  }
}

// 存储键名常量
export const STORAGE_KEYS = {
  AUTH_DATA: 'auth_data',
  USER_INFO: 'user_info',
  APP_CONFIG: 'app_config'
}
