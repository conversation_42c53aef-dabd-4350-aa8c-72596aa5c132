/**
 * 时间处理工具函数
 * 用于头像更新时间验证等功能
 */

/**
 * 检查是否可以更新头像/昵称
 * @param {string|number|Date} lastAvatarTime 上次更新时间
 * @param {number} cooldownMinutes 冷却时间（分钟），默认0表示无限制
 * @returns {object} 验证结果
 */
export function canUpdateAvatar(lastAvatarTime, cooldownMinutes = 0) {
  try {
    console.log('🕐 检查头像更新时间限制:')
    console.log('  - lastAvatarTime:', lastAvatarTime)
    console.log('  - cooldownMinutes:', cooldownMinutes)
    
    // 如果没有上次更新时间，允许更新
    if (!lastAvatarTime) {
      console.log('✅ 没有上次更新时间记录，允许更新')
      return {
        canUpdate: true,
        reason: '首次更新'
      }
    }
    
    // 如果没有冷却时间限制，允许更新
    if (cooldownMinutes <= 0) {
      console.log('✅ 没有时间限制，允许更新')
      return {
        canUpdate: true,
        reason: '无时间限制'
      }
    }
    
    // 转换时间格式
    const lastTime = parseTime(lastAvatarTime)
    const currentTime = new Date()
    
    console.log('📅 时间对比:')
    console.log('  - 上次更新时间:', lastTime.toISOString())
    console.log('  - 当前时间:', currentTime.toISOString())
    
    // 计算时间差（毫秒）
    const timeDiff = currentTime.getTime() - lastTime.getTime()
    const timeDiffMinutes = timeDiff / (1000 * 60)
    
    console.log('⏱️ 时间差:', timeDiffMinutes, '分钟')
    console.log('🔒 需要等待:', cooldownMinutes, '分钟')
    
    if (timeDiffMinutes >= cooldownMinutes) {
      console.log('✅ 冷却时间已过，允许更新')
      return {
        canUpdate: true,
        reason: '冷却时间已过',
        timeDiffMinutes: Math.floor(timeDiffMinutes)
      }
    } else {
      const remainingMinutes = Math.ceil(cooldownMinutes - timeDiffMinutes)
      console.log('❌ 冷却时间未过，还需等待:', remainingMinutes, '分钟')
      return {
        canUpdate: false,
        reason: '冷却时间未过',
        remainingMinutes: remainingMinutes,
        timeDiffMinutes: Math.floor(timeDiffMinutes)
      }
    }
    
  } catch (error) {
    console.error('❌ 时间验证出错:', error)
    // 出错时默认允许更新，避免阻塞用户
    return {
      canUpdate: true,
      reason: '时间验证出错，默认允许',
      error: error.message
    }
  }
}

/**
 * 解析时间字符串/数字为Date对象
 * @param {string|number|Date} timeValue 时间值
 * @returns {Date} Date对象
 */
export function parseTime(timeValue) {
  if (!timeValue) {
    throw new Error('时间值不能为空')
  }
  
  // 如果已经是Date对象
  if (timeValue instanceof Date) {
    return timeValue
  }
  
  // 如果是数字（时间戳）
  if (typeof timeValue === 'number') {
    // 判断是秒级还是毫秒级时间戳
    const timestamp = timeValue < 10000000000 ? timeValue * 1000 : timeValue
    return new Date(timestamp)
  }
  
  // 如果是字符串
  if (typeof timeValue === 'string') {
    // 尝试直接解析
    const parsed = new Date(timeValue)
    if (isNaN(parsed.getTime())) {
      throw new Error(`无法解析时间字符串: ${timeValue}`)
    }
    return parsed
  }
  
  throw new Error(`不支持的时间格式: ${typeof timeValue}`)
}

/**
 * 格式化剩余时间为友好的文本
 * @param {number} minutes 剩余分钟数
 * @returns {string} 格式化的时间文本
 */
export function formatRemainingTime(minutes) {
  if (minutes <= 0) {
    return '现在可以更新'
  }
  
  if (minutes < 60) {
    return `还需等待 ${minutes} 分钟`
  }
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  
  if (remainingMinutes === 0) {
    return `还需等待 ${hours} 小时`
  }
  
  return `还需等待 ${hours} 小时 ${remainingMinutes} 分钟`
}

/**
 * 检查用户信息中的头像更新时间限制
 * @param {object} userInfo 用户信息
 * @param {number} cooldownMinutes 冷却时间（分钟）
 * @returns {object} 验证结果
 */
export function checkUserAvatarUpdateTime(userInfo, cooldownMinutes = 0) {
  console.log('👤 检查用户头像更新时间限制:')
  console.log('  - userInfo:', userInfo)
  
  if (!userInfo || typeof userInfo !== 'object') {
    console.warn('⚠️ 用户信息无效')
    return {
      canUpdate: true,
      reason: '用户信息无效，默认允许'
    }
  }
  
  // 检查 last_avatar_time 字段
  const lastAvatarTime = userInfo.last_avatar_time || userInfo.lastAvatarTime
  
  return canUpdateAvatar(lastAvatarTime, cooldownMinutes)
}

/**
 * 获取下次可更新的时间
 * @param {string|number|Date} lastAvatarTime 上次更新时间
 * @param {number} cooldownMinutes 冷却时间（分钟）
 * @returns {Date|null} 下次可更新的时间，如果可以立即更新则返回null
 */
export function getNextUpdateTime(lastAvatarTime, cooldownMinutes = 0) {
  if (!lastAvatarTime || cooldownMinutes <= 0) {
    return null // 可以立即更新
  }
  
  try {
    const lastTime = parseTime(lastAvatarTime)
    const nextTime = new Date(lastTime.getTime() + cooldownMinutes * 60 * 1000)
    return nextTime
  } catch (error) {
    console.error('计算下次更新时间失败:', error)
    return null
  }
}

/**
 * 格式化时间为本地时间字符串
 * @param {Date} date Date对象
 * @returns {string} 格式化的时间字符串
 */
export function formatDateTime(date) {
  if (!date || !(date instanceof Date)) {
    return '--'
  }
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

/**
 * 时间验证配置
 */
export const TIME_CONFIG = {
  // 头像更新冷却时间（分钟）
  AVATAR_COOLDOWN_MINUTES: 0, // 默认无限制，可根据需要调整
  
  // 昵称更新冷却时间（分钟）
  NICKNAME_COOLDOWN_MINUTES: 0, // 默认无限制，可根据需要调整
  
  // 时间格式
  DATE_FORMAT: 'YYYY-MM-DD HH:mm:ss'
}

export default {
  canUpdateAvatar,
  parseTime,
  formatRemainingTime,
  checkUserAvatarUpdateTime,
  getNextUpdateTime,
  formatDateTime,
  TIME_CONFIG
}
