/**
 * 认证相关API接口
 *
 * API返回格式:
 * {
 *   code: 200,
 *   msg: '登录成功',
 *   results: {
 *     jwtToken: {
 *       token: string,    // JWT访问令牌
 *       refresh: string   // JWT刷新令牌
 *     },
 *     user: {
 *       id: number,
 *       nickname: string,
 *       avatar: string,
 *       phone: string,
 *       openid: string,
 *       role: 'user' | 'agent' | 'miaochang'
 *     }
 *   }
 * }
 */
import { request } from '../utils/request'

export const authAPI = {
  /**
   * 微信小程序登录
   * @param {string} code 微信登录code
   * @returns {Promise} 登录结果
   * @example
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '登录成功',
   *   results: {
   *     jwtToken: { token: 'xxx', refresh: 'xxx' },
   *     user: { id: 1, nickname: 'xxx', role: 'user', ... }
   *   }
   * }
   */
  wechatLogin(code) {
    return request.post('/api/wechat/login/', { code })
  },
  
  /**
   * 验证JWT token
   * @param {string} token JWT token
   * @returns {Promise} 验证结果
   * @example
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '验证成功',
   *   results: {
   *     jwtToken: { token: 'new_token', refresh: 'new_refresh' },
   *     user: { id: 1, nickname: 'xxx', role: 'user', ... }
   *   }
   * }
   */
  verifyJWT(token) {
    return request.get('/api/wechat/verify/jwt/', {}, {
      header: {
        'Authorization': `Bearer ${token}`
      }
    })
  },
  
  /**
   * 刷新JWT token
   * @param {string} refreshToken 刷新token
   * @returns {Promise} 刷新结果
   * @example
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '刷新成功',
   *   results: {
   *     jwtToken: { token: 'new_token', refresh: 'new_refresh' },
   *     user: { id: 1, nickname: 'xxx', role: 'user', ... }
   *   }
   * }
   */
  refreshJWT(refreshToken) {
    return request.post('/api/wechat/refresh/jwt/', {
      refresh: refreshToken
    })
  }
}

/**
 * 微信相关API接口
 */
export const wechatAPI = {
  /**
   * 获取或创建邀请代理码
   * @returns {Promise} 代理码信息
   */
  getInviteAgent() {
    return request.get('/api/wechat/invite/agent/get/')
  },
  
  /**
   * 检查代理码有效性
   * @param {string} agentCode 代理邀请码
   * @returns {Promise} 检查结果
   */
  checkAgentCode(agentCode) {
    return request.post('/api/wechat/agent/code/verify/', {
      code: agentCode
    })
  },

  /**
   * 确认激活代理
   * @param {Object} agentInfo 代理信息对象
   * @returns {Promise} 确认结果
   */
  confirmAgentActivation(agentInfo) {
    return request.post('/api/wechat/agent/confirm/', agentInfo)
  },

  /**
   * 获取预种子老板二维码
   * @returns {Promise} 二维码信息
   */
  getPreseedBossQrCode() {
    return request.get('/api/wechat/preseed/boss/qrcode/get/')
  },

  /**
   * 验证邀请代理码
   * @param {string} agentCode 代理邀请码
   * @returns {Promise} 验证结果
   */
  verifyInviteAgent(agentCode) {
    return request.post('/api/wechat/invite/agent/verify/', {
      agent_code: agentCode
    })
  },
  
  /**
   * 获取微信喵厂激活码信息
   * @param {string} code 激活码
   * @returns {Promise} 激活码信息
   */
  getMiaochangActivate(code) {
    return request.get('/api/wechat/miaochang/activate/get/', { code })
  },

  /**
   * 确认激活微信喵厂
   * @param {number} id 激活码ID
   * @returns {Promise} 激活确认结果
   */
  confirmMiaochangActivate(id) {
    return request.post('/api/wechat/miaochang/activate/confirm/', { id })
  },
  
  /**
   * 创建安卓登录二维码
   * @param {number} userId 用户ID
   * @returns {Promise} 二维码信息
   */
  createAndroidLoginQrcode(userId) {
    return request.post('/api/wechat/android/login/qrcode/create/', {
      user_id: userId
    })
  }
}

/**
 * 试水相关API接口
 */
export const preSeedAPI = {
  /**
   * 获取微信试水记录
   * @param {string} id 试水记录ID
   * @returns {Promise} 试水记录
   */
  retrievePreSeedLog(id) {
    return request.post('/api/preSeed/wechat/retrieve_pre_seed_log/', { id })
  },

  /**
   * 验证预种子二维码
   * @param {string} no 预种子编号
   * @returns {Promise} 验证结果
   * @example
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '验证成功',
   *   results: {
   *     miaochang: { name: '苗场名称' },
   *     quantity: 100
   *   }
   * }
   */
  verifyQrcode(no) {
    return request.post('/api/wechat/preseed/qrcode/verify/', { no })
  },

  /**
   * 确认预种子操作
   * @param {Object} results 验证结果对象
   * @returns {Promise} 确认结果
   * @example
   * // 返回格式:
   * {
   *   code: 200,
   *   msg: '确认成功',
   *   results: { ... }
   * }
   */
  confirmQrcode(results) {
    return request.post('/api/wechat/preseed/qrcode/confirm/', results)
  }
}
