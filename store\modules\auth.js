import { AUTH_TYPES } from '../types'
import { storageUtils, STORAGE_KEYS } from '../../utils/storage'
import { debug } from '../../utils/debug'
import { adaptJwtVerifyResponse, adaptWechatLoginResponse } from '../../utils/apiAdapter'
import { debugJwtVerifyResponse } from '../../utils/debugHelper'

const authModule = {
  namespaced: true,
  
  state: {
    token: null,
    refreshToken: null,
    lastVerifyTime: null,
    isLoggedIn: false,
    loginLoading: false,
    hasLaunched: false
  },
  
  mutations: {
    [AUTH_TYPES.SET_TOKEN](state, { token, refreshToken }) {
      state.token = token
      state.refreshToken = refreshToken
      state.isLoggedIn = !!token
    },
    
    [AUTH_TYPES.SET_LAST_VERIFY_TIME](state, timestamp) {
      state.lastVerifyTime = timestamp
    },
    
    [AUTH_TYPES.SET_LOGIN_LOADING](state, loading) {
      state.loginLoading = loading
    },

    [AUTH_TYPES.SET_HAS_LAUNCHED](state, hasLaunched) {
      state.hasLaunched = hasLaunched
    },

    [AUTH_TYPES.CLEAR_AUTH](state) {
      state.token = null
      state.refreshToken = null
      state.lastVerifyTime = null
      state.isLoggedIn = false
    }
  },
  
  actions: {
    /**
     * 静默登录主流程
     */
    async silentLogin({ commit, dispatch, state }) {
      // 防止重复请求
      if (state.loginLoading) {
        debug.warn('auth', '登录请求正在进行中，忽略重复请求')
        return { success: false, message: '登录请求正在进行中' }
      }

      commit(AUTH_TYPES.SET_LOGIN_LOADING, true)

      try {
        debug.action('auth', 'silentLogin', '开始静默登录流程')
        
        // 1. 检查本地token
        const localAuth = await dispatch('loadLocalAuth')
        if (!localAuth.token) {
          debug.log('auth', '本地无token，执行完整登录')
          return await dispatch('fullLogin')
        }
        
        // 2. 检查60分钟缓存
        const now = Date.now()
        const timeDiff = now - (localAuth.lastVerifyTime || 0)
        const CACHE_DURATION = 1 * 60 * 1000 // 60分钟
        
        if (timeDiff < CACHE_DURATION) {
          debug.log('auth', '使用缓存token，距离上次验证:', Math.floor(timeDiff / 1000 / 60), '分钟')
          // 直接使用缓存的token
          commit(AUTH_TYPES.SET_TOKEN, localAuth)
          commit(AUTH_TYPES.SET_LAST_VERIFY_TIME, localAuth.lastVerifyTime)
          await dispatch('user/setUserInfo', localAuth.userInfo, { root: true })
          return { success: true, fromCache: true }
        }

        debug.log('auth', '缓存已过期，验证token有效性')
        // 3. 验证token有效性
        return await dispatch('verifyToken', localAuth.token)
        
      } catch (error) {
        console.error('静默登录失败:', error)
        return await dispatch('fullLogin')
      } finally {
        commit(AUTH_TYPES.SET_LOGIN_LOADING, false)
        commit(AUTH_TYPES.SET_HAS_LAUNCHED, true)
      }
    },
    
    /**
     * 验证token
     */
    async verifyToken({ commit, dispatch }, token) {
      try {
        console.log('验证token有效性')

        // 调用API验证token
        const { authAPI } = await import('../../api/auth')
        const response = await authAPI.verifyJWT(token)

        // 调试JWT验证响应
        debugJwtVerifyResponse(response)

        // 使用适配器处理响应
        const adaptedData = adaptJwtVerifyResponse(response)

        if (adaptedData.success) {
          const { token: newToken, refresh, user } = adaptedData
          
          // 更新token和时间戳
          commit(AUTH_TYPES.SET_TOKEN, { token: newToken, refreshToken: refresh })
          commit(AUTH_TYPES.SET_LAST_VERIFY_TIME, Date.now())
          
          // 保存到本地
          await dispatch('saveLocalAuth', { 
            token: newToken, 
            refreshToken: refresh,
            userInfo: user 
          })
          
          // 更新用户信息
          await dispatch('user/setUserInfo', user, { root: true })
          
          console.log('token验证成功')
          return { success: true, fromCache: false }
        } else {
          console.error('❌ Token验证失败: 适配器返回失败结果')
          console.error('📄 验证结果详情:', JSON.stringify(adaptedData, null, 2))
          throw new Error(adaptedData.error || 'Token验证失败')
        }
      } catch (error) {
        console.error('❌ Token验证异常:', error.message)
        console.error('📄 错误详情:', error)

        // 如果是网络错误或服务器错误，不清除认证信息，而是返回失败
        if (error.message.includes('网络') || error.message.includes('服务器')) {
          console.log('🔄 网络错误，保留认证信息，稍后重试')
          return { success: false, error: error.message, shouldRetry: true }
        }

        // 其他错误清除认证信息并尝试完整登录
        console.log('🔄 清除认证信息，尝试完整登录')
        await dispatch('clearAuth')
        return await dispatch('fullLogin')
      }
    },
    
    /**
     * 完整登录流程
     */
    async fullLogin({ commit, dispatch }) {
      try {
        console.log('执行完整登录流程')
        
        // 1. 获取微信登录code
        const loginRes = await new Promise((resolve, reject) => {
          uni.login({
            provider: 'weixin',
            success: resolve,
            fail: reject
          })
        })

        if (!loginRes.code) {
          throw new Error('获取微信登录code失败')
        }

        console.log('获取微信code成功:', loginRes.code)
        
        // 2. 调用登录接口
        const { authAPI } = await import('../../api/auth')
        const response = await authAPI.wechatLogin(loginRes.code)

        // 使用适配器处理响应
        const adaptedData = adaptWechatLoginResponse(response)

        if (!adaptedData.success) {
          throw new Error(adaptedData.error || '登录失败')
        }

        const { token, refresh, user } = adaptedData
        
        // 3. 保存认证信息
        commit(AUTH_TYPES.SET_TOKEN, { token, refreshToken: refresh })
        commit(AUTH_TYPES.SET_LAST_VERIFY_TIME, Date.now())
        
        // 4. 保存到本地
        await dispatch('saveLocalAuth', { 
          token, 
          refreshToken: refresh,
          userInfo: user 
        })
        
        // 5. 更新用户信息
        await dispatch('user/setUserInfo', user, { root: true })
        
        console.log('完整登录成功')
        return { success: true, fromCache: false }
        
      } catch (error) {
        console.error('完整登录失败:', error)
        uni.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
        throw error
      }
    },
    
    /**
     * 加载本地认证信息
     */
    async loadLocalAuth() {
      try {
        const authData = storageUtils.getItem(STORAGE_KEYS.AUTH_DATA, {})
        console.log('加载本地认证信息:', authData ? '存在' : '不存在')
        return authData
      } catch (error) {
        console.error('加载本地认证信息失败:', error)
        return {}
      }
    },
    
    /**
     * 保存本地认证信息
     */
    async saveLocalAuth({ commit }, authData) {
      try {
        const dataToSave = {
          ...authData,
          lastVerifyTime: Date.now()
        }
        
        storageUtils.setItem(STORAGE_KEYS.AUTH_DATA, dataToSave)
        commit(AUTH_TYPES.SET_LAST_VERIFY_TIME, dataToSave.lastVerifyTime)
        console.log('保存本地认证信息成功')
      } catch (error) {
        console.error('保存本地认证信息失败:', error)
      }
    },
    
    /**
     * 清除认证信息
     */
    async clearAuth({ commit, dispatch }) {
      commit(AUTH_TYPES.CLEAR_AUTH)
      storageUtils.removeItem(STORAGE_KEYS.AUTH_DATA)
      await dispatch('user/clearUserInfo', null, { root: true })
      console.log('清除认证信息成功')
    }
  },
  
  getters: {
    isTokenValid: (state) => {
      return !!state.token && state.isLoggedIn
    },

    isLoggedIn: (state) => {
      return state.isLoggedIn
    },

    shouldVerifyToken: (state) => {
      if (!state.lastVerifyTime) return true
      const now = Date.now()
      const timeDiff = now - state.lastVerifyTime
      return timeDiff >= 1 * 60 * 1000 // 1分钟
    }
  }
}

export default authModule
