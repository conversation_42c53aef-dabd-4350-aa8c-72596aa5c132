<template>
	<view class="container">
		<!-- 认证等待状态 -->
		<view v-if="!showPageContent" class="auth-waiting">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ pageStatusText }}</text>
			</view>
		</view>

		<!-- 页面主要内容 - 只有认证成功后才显示 -->
		<view v-if="showPageContent" class="main-content">
			<!-- <view class="header">
				<text class="title">激活苗场主</text>
				<text class="subtitle">扫码激活苗场主身份</text>
			</view> -->

			<view class="content">
				<!-- 激活码显示区域 -->
				<view class="activate-code-section" v-if="activateCode">
					<view class="code-header">
						<text class="code-title">🌾 苗场主激活码</text>
						<text class="code-subtitle">请确认以下激活码信息</text>
					</view>
					<!-- <view class="code-display">
						<text class="code-label">激活码:</text>
						<text class="code-value">{{ activateCode }}</text>
					</view> -->
					<!-- <view class="code-status" v-if="!result">
						<text class="status-text">正在验证激活码...</text>
					</view> -->
				</view>

				<!-- 无激活码提示 -->
				<view class="no-code-section" v-if="!activateCode && !result">
					<view class="no-code-icon">📱</view>
					<text class="no-code-title">未检测到激活码</text>
					<!-- <text class="no-code-desc">请通过扫描苗场主激活二维码进入此页面</text> -->
				</view>

				<!-- 激活码详情显示 (code==200时) -->
				<view class="activate-details" v-if="activateInfo && !result">
					<!-- <view class="details-header">
						<text class="details-title">🏢 激活码详情</text>
						<text class="details-subtitle">请确认以下信息无误</text>
					</view> -->

					<view class="details-card">
						<view class="detail-item">
							<text class="detail-label">机构名称:</text>
							<text class="detail-value">{{ activateInfo.name }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">联系电话:</text>
							<text class="detail-value">{{ activateInfo.phone }}</text>
						</view>
						<view class="detail-item" v-if="activateInfo.city">
							<text class="detail-label">所在城市:</text>
							<text class="detail-value">{{ activateInfo.city.name }}</text>
						</view>
						<view class="detail-item" v-if="activateInfo.expiredTime">
							<text class="detail-label">有效期至:</text>
							<text class="detail-value">{{ formatDate(activateInfo.expiredTime) }}</text>
						</view>
					</view>

					<view class="confirm-section">
						<button
							class="confirm-btn"
							@click="confirmActivation"
							:disabled="activating"
						>
							<text v-if="activating">激活中...</text>
							<text v-else>确认激活</text>
						</button>
					</view>
				</view>

				<!-- 激活结果显示 -->
				<view class="result" v-if="result">
					<view class="result-icon">{{ result.includes('恭喜') ? '🎉' : '❌' }}</view>
					<text class="result-text">{{ result }}</text>
				</view>

				<button class="btn" @click="goBack">返回首页</button>
			</view>
		</view>

		<!-- 认证失败状态 -->
		<view v-if="authReady && !authSuccess" class="auth-failed">
			<view class="error-container">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{ authError || '登录验证失败' }}</text>
				<text class="error-desc">无法处理苗场主激活，请重新登录</text>
				<button class="retry-btn" @click="goBack">返回首页</button>
			</view>
		</view>
	</view>
</template>

<script>
import { wechatAPI } from '../../../api/auth'
import { mapActions } from 'vuex'
import pageEventMixin from '@/mixins/pageEvent.js'

export default {
	name: 'MiaoChangActivePage',
	mixins: [pageEventMixin],

	data() {
		return {
			activateCode: '',
			result: '',
			activateInfo: null,  // 激活码详细信息
			showConfirm: false,  // 是否显示确认按钮
			activating: false    // 是否正在激活中
		}
	},

	onLoad(options) {
		console.log('苗场主激活页面加载，参数:', options)

		// 解析微信小程序码的scene参数
		this.parseSceneParams(options)

		// pageEventMixin 会自动保存参数并设置事件监听
	},

	methods: {
		...mapActions('user', ['setUserInfo']),

		/**
		 * 解析微信小程序码的scene参数
		 * 支持格式: scene="code=ABC123" 或直接 code="ABC123"
		 */
		parseSceneParams(options) {
			console.log('🔍 开始解析页面参数:', options)

			let extractedCode = null

			// 方式1: 直接从code参数获取 (普通链接跳转)
			if (options.code) {
				extractedCode = options.code
				console.log('✅ 从code参数获取激活码:', extractedCode)
			}
			// 方式2: 从scene参数解析 (微信小程序码扫码)
			else if (options.scene) {
				console.log('🔍 检测到scene参数:', options.scene)

				try {
					// 解码scene参数 (可能被URL编码)
					const decodedScene = decodeURIComponent(options.scene)
					console.log('🔓 解码后的scene:', decodedScene)

					// 解析 "code=ABC123" 格式
					const codeMatch = decodedScene.match(/code=([^&]+)/)
					if (codeMatch && codeMatch[1]) {
						extractedCode = codeMatch[1]
						console.log('✅ 从scene参数解析出激活码:', extractedCode)
					} else {
						console.warn('⚠️ scene参数格式不正确:', decodedScene)
					}
				} catch (error) {
					console.error('❌ 解析scene参数失败:', error)
				}
			}

			// 保存解析出的激活码
			if (extractedCode) {
				this.activateCode = extractedCode
				console.log('💾 激活码已保存:', this.activateCode)

				// 更新pageOptions以便后续使用
				if (!this.pageOptions) {
					this.pageOptions = {}
				}
				this.pageOptions.code = extractedCode
			} else {
				console.log('📝 未找到有效的激活码参数')
			}

			return extractedCode
		},
		// 安全的处理苗场主激活 - 只有在认证成功后才能执行
		async handleMiaochangActivation(code) {
			if (!this.canMakeNetworkRequest) {
				console.warn('🚫 认证未完成，无法处理苗场主激活')
				this.result = '认证未完成，无法处理苗场主激活'
				return
			}

			// 调试：检查当前认证状态
			this.debugAuthStatus()

			try {
				uni.showLoading({
					title: '激活中...'
				})

				console.log('🌾 开始安全的苗场主激活请求')
				console.log('📋 激活码:', code)

				// 使用安全的网络请求方法
				const response = await this.safeNetworkRequest(wechatAPI.getMiaochangActivate, code)

				console.log('📥 激活API响应:', response)

				if (response.code === 200) {
					// 显示激活码详情，等待用户确认
					this.activateInfo = response.results
					this.showConfirm = true

					console.log('✅ 激活码有效，显示详情:', this.activateInfo)

					uni.showToast({
						title: '激活码验证成功',
						icon: 'success'
					})
				} else {
					// 显示错误信息
					this.result = response.msg || '激活码无效'
					uni.showToast({
						title: response.msg || '激活失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('❌ 苗场主激活失败:', error)

				// 根据错误类型提供更具体的错误信息
				let errorMessage = '网络错误，请重试'
				if (error.message) {
					if (error.message.includes('401') || error.message.includes('Unauthorized')) {
						errorMessage = '登录已过期，请重新登录'
					} else if (error.message.includes('网络')) {
						errorMessage = '网络连接失败，请检查网络'
					} else {
						errorMessage = error.message
					}
				}

				this.result = errorMessage
				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 调试认证状态
		 */
		debugAuthStatus() {
			console.log('🔍 调试认证状态:')
			console.log('  - authReady:', this.authReady)
			console.log('  - authSuccess:', this.authSuccess)
			console.log('  - canMakeNetworkRequest:', this.canMakeNetworkRequest)

			// 检查store状态
			if (this.$store) {
				const authState = this.$store.state.auth
				console.log('  - store.auth.token:', authState.token ? '存在' : '不存在')
				console.log('  - store.auth.isLoggedIn:', authState.isLoggedIn)
				console.log('  - store.auth.hasLaunched:', authState.hasLaunched)
			}

			// 检查本地存储
			try {
				const authData = uni.getStorageSync('auth_data')
				console.log('  - storage.auth_data:', authData ? '存在' : '不存在')
				if (authData) {
					console.log('  - storage.token:', authData.token ? '存在' : '不存在')
				}
			} catch (error) {
				console.log('  - storage检查失败:', error)
			}
		},

		/**
		 * 确认激活苗场主
		 */
		async confirmActivation() {
			if (this.activating || !this.activateInfo || !this.activateInfo.id) {
				return
			}

			try {
				this.activating = true
				uni.showLoading({
					title: '正在激活...'
				})

				console.log('🎯 用户确认激活，开始激活流程')
				console.log('📋 激活码ID:', this.activateInfo.id)

				// 调用激活确认API
				const confirmResponse = await this.safeNetworkRequest(
					wechatAPI.confirmMiaochangActivate,
					this.activateInfo.id
				)

				console.log('📥 激活确认API响应:', confirmResponse)

				if (confirmResponse.code === 200) {
					console.log('✅ 激活确认成功，处理返回的用户信息和token')

					// 使用适配器处理激活确认响应（和verifyJwt一样的格式）
					await this.handleActivationSuccess(confirmResponse)

					// 激活成功
					this.result = '恭喜您成为苗场主！'
					this.activateInfo = null
					this.showConfirm = false

					uni.showToast({
						title: '激活成功',
						icon: 'success',
						duration: 2000
					})

					// 延迟跳转到首页
					setTimeout(() => {
						console.log('🏠 激活成功，跳转到首页')
						this.goBack()
					}, 2500)

				} else {
					console.error('❌ 激活确认失败:', confirmResponse)
					throw new Error(confirmResponse.msg || '激活确认失败')
				}

			} catch (error) {
				console.error('❌ 激活失败:', error)

				let errorMessage = '激活失败，请重试'
				if (error.message) {
					if (error.message.includes('401') || error.message.includes('Unauthorized')) {
						errorMessage = '登录已过期，请重新登录'
					} else if (error.message.includes('网络')) {
						errorMessage = '网络连接失败，请检查网络'
					} else {
						errorMessage = error.message
					}
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				this.activating = false
				uni.hideLoading()
			}
		},

		/**
		 * 处理激活成功的响应 - 参考login和verifyJwt的处理方式
		 */
		async handleActivationSuccess(response) {
			try {
				console.log('🎯 处理激活成功响应，使用适配器解析数据')
				console.log('📥 激活响应原始数据:', response)

				// 导入适配器
				const { adaptJwtVerifyResponse } = await import('../../../utils/apiAdapter')

				// 使用JWT验证适配器处理响应（格式相同）
				const adaptedData = adaptJwtVerifyResponse(response)

				if (adaptedData.success) {
					const { token, refresh, user } = adaptedData

					console.log('✅ 激活响应数据解析成功:')
					console.log('  - token:', token ? '存在' : '不存在')
					console.log('  - refresh:', refresh ? '存在' : '不存在')
					console.log('  - user:', user ? '存在' : '不存在')
					console.log('  - user.role:', user?.role)

					// 1. 更新store中的token
					await this.$store.commit('auth/SET_TOKEN', {
						token,
						refreshToken: refresh
					})

					// 2. 更新验证时间
					await this.$store.commit('auth/SET_LAST_VERIFY_TIME', Date.now())

					// 3. 保存到本地存储
					await this.$store.dispatch('auth/saveLocalAuth', {
						token,
						refreshToken: refresh,
						userInfo: user
					})

					// 4. 更新用户信息到store
					await this.setUserInfo(user)

					console.log('✅ 激活成功，用户信息和token已更新')
					console.log('👤 新的用户角色:', user.role)

				} else {
					console.error('❌ 激活响应适配失败:', adaptedData.error)
					throw new Error(adaptedData.error || '激活响应数据格式错误')
				}

			} catch (error) {
				console.error('❌ 处理激活成功响应失败:', error)
				throw error
			}
		},

		/**
		 * 格式化日期 - iOS兼容版本
		 */
		formatDate(dateString) {
			if (!dateString) return '--'

			try {
				console.log('📅 开始格式化日期:', dateString)

				// 使用更强大的日期解析方法
				const date = this.parseDate(dateString)

				if (!date || isNaN(date.getTime())) {
					console.error('❌ 日期解析失败:', dateString)
					return dateString
				}

				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')

				const formatted = `${year}-${month}-${day}`
				console.log('✅ 日期格式化成功:', dateString, '->', formatted)

				return formatted
			} catch (error) {
				console.error('❌ 日期格式化失败:', error, '原始日期:', dateString)
				return dateString
			}
		},

		/**
		 * 解析日期字符串 - 兼容多种格式
		 */
		parseDate(dateString) {
			if (!dateString || typeof dateString !== 'string') {
				return null
			}

			// 去除首尾空格
			const trimmed = dateString.trim()

			// 方法1: 手动解析 "yyyy-MM-dd HH:mm:ss" 格式
			const match1 = trimmed.match(/^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/)
			if (match1) {
				const [, year, month, day, hour, minute, second] = match1
				console.log('🔍 使用方法1解析:', trimmed)
				return new Date(parseInt(year), parseInt(month) - 1, parseInt(day),
								parseInt(hour), parseInt(minute), parseInt(second))
			}

			// 方法2: 手动解析 "yyyy-MM-dd" 格式
			const match2 = trimmed.match(/^(\d{4})-(\d{2})-(\d{2})$/)
			if (match2) {
				const [, year, month, day] = match2
				console.log('🔍 使用方法2解析:', trimmed)
				return new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
			}

			// 方法3: iOS兼容格式转换
			let iosCompatible = trimmed
			if (/^\d{4}-\d{2}-\d{2}/.test(trimmed)) {
				// 将 "yyyy-MM-dd" 转换为 "yyyy/MM/dd"
				iosCompatible = trimmed.replace(/-/g, '/')
				console.log('🍎 iOS兼容转换:', trimmed, '->', iosCompatible)
			}

			// 方法4: 尝试直接解析
			try {
				const date = new Date(iosCompatible)
				if (!isNaN(date.getTime())) {
					console.log('🔍 使用方法4解析成功:', iosCompatible)
					return date
				}
			} catch (error) {
				console.warn('方法4解析失败:', error)
			}

			console.error('❌ 所有日期解析方法都失败了:', dateString)
			return null
		},

		// 返回首页
		goBack() {
			uni.reLaunch({
				url: '/pages/index/index'
			})
		},

		/**
		 * 认证完成回调 - 由 App.vue 事件触发
		 * 只有认证成功后才会执行
		 */
		onAuthReady(authData) {
			console.log('🌾 苗场主页面收到认证完成事件:', authData)
			console.log('✅ 苗场主页面现在可以安全地处理激活逻辑')

			// 认证完成后安全地处理页面参数
			const codeToProcess = this.activateCode || (this.pageOptions && this.pageOptions.code)

			if (codeToProcess) {
				console.log('🔍 开始处理苗场主激活码:', codeToProcess)
				this.handleMiaochangActivation(codeToProcess)
			} else {
				console.log('📝 没有苗场主激活码参数')
				// 显示提示信息，引导用户通过正确方式进入
				this.result = '请通过扫描苗场主激活二维码进入此页面'
			}
		},

		/**
		 * 认证失败回调 - 由 App.vue 事件触发
		 */
		onAuthFailed(errorData) {
			console.log('🌾 苗场主页面收到认证失败事件:', errorData)
			console.log('❌ 苗场主页面无法处理激活逻辑，显示错误状态')

			// 认证失败时的错误信息已经由 mixin 处理
			// 页面内容已经被隐藏，显示错误状态
		}
	}
}
</script>

<style>
.container {
	padding: 40rpx;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 48rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
}

.content {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

/* 激活码显示区域 */
.activate-code-section {
	margin-bottom: 40rpx;
}

.code-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.code-title {
	display: block;
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
	margin-bottom: 10rpx;
}

.code-subtitle {
	font-size: 24rpx;
	color: #666;
}

.code-display {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 15rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	text-align: center;
}

.code-label {
	display: block;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-bottom: 10rpx;
}

.code-value {
	display: block;
	font-size: 36rpx;
	color: #fff;
	font-weight: bold;
	font-family: 'Courier New', monospace;
	letter-spacing: 2rpx;
	word-break: break-all;
}

.code-status {
	text-align: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 10rpx;
}

.status-text {
	font-size: 26rpx;
	color: #666;
}

/* 无激活码提示 */
.no-code-section {
	text-align: center;
	padding: 60rpx 30rpx;
	margin-bottom: 40rpx;
}

.no-code-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}

.no-code-title {
	display: block;
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
	margin-bottom: 15rpx;
}

.no-code-desc {
	font-size: 26rpx;
	color: #999;
	line-height: 1.5;
}

/* 激活码详情显示 */
.activate-details {
	margin-bottom: 40rpx;
}

.details-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.details-title {
	display: block;
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
	margin-bottom: 10rpx;
}

.details-subtitle {
	font-size: 24rpx;
	color: #666;
}

.details-card {
	background-color: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #e0e0e0;
}

.detail-item:last-child {
	border-bottom: none;
}

.detail-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.detail-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
	text-align: right;
	flex: 1;
	margin-left: 20rpx;
}

.confirm-section {
	text-align: center;
}

.confirm-btn {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
	color: #fff;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 600;
	border: none;
	box-shadow: 0 4rpx 15rpx rgba(82, 196, 26, 0.3);
	transition: all 0.3s ease;
}

.confirm-btn:disabled {
	background: #d9d9d9;
	color: #999;
	box-shadow: none;
}

.confirm-btn:active:not(:disabled) {
	transform: scale(0.98);
}

/* 激活结果显示 */
.result {
	margin-bottom: 40rpx;
	padding: 40rpx 30rpx;
	background-color: #e8f5e8;
	border-radius: 15rpx;
	text-align: center;
}

.result-icon {
	display: block;
	font-size: 60rpx;
	margin-bottom: 20rpx;
}

.result-text {
	font-size: 32rpx;
	color: #4cd964;
	font-weight: 600;
}

.btn {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 44rpx;
	font-size: 32rpx;
	border: none;
}

/* 认证等待和失败状态样式 */
.auth-waiting, .auth-failed {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.loading-container, .error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
	background-color: #fff;
	border-radius: 20rpx;
	margin: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid #e0e0e0;
	border-top: 6rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text, .error-text {
	font-size: 28rpx;
	text-align: center;
	margin-bottom: 20rpx;
}

.loading-text {
	color: #666;
}

.error-text {
	color: #dd524d;
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}

.error-desc {
	font-size: 24rpx;
	color: #999;
	text-align: center;
	margin-bottom: 40rpx;
}

.retry-btn {
	width: 200rpx;
	height: 70rpx;
	line-height: 70rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: none;
}

.main-content {
	min-height: 100vh;
}
</style>
