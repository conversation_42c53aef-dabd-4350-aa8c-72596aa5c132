<template>
	<view class="preseed-logs-page">
		<!-- 认证等待状态 -->
		<view v-if="!authReady" class="auth-loading">
			<view class="loading-spinner"></view>
			<text class="loading-text">正在验证身份...</text>
		</view>

		<!-- 认证失败状态 -->
		<view v-else-if="authError" class="auth-error">
			<text class="error-text">{{ authError }}</text>
			<button @click="retryAuth" class="retry-btn">重试</button>
		</view>

		<!-- 页面内容 -->
		<view v-else class="page-content">
			<!-- 管理员操作按钮 -->
			<view v-if="showAdminButton" class="admin-section">
				<button @click="goToQrCodeGenerate" class="admin-btn">
					<text class="btn-text">生成试水苗二维码</text>
				</button>
			</view>

			<!-- 列表容器 -->
			<scroll-view 
				class="scroll-container"
				scroll-y
				@scrolltolower="loadMore"
				refresher-enabled
				@refresherrefresh="onRefresh"
				:refresher-triggered="refreshing"
			>
				<!-- 预种记录列表 -->
				<view class="preseed-list">
					<view
						v-for="record in preseedList"
						:key="record.id"
						class="preseed-card"
					>
						<!-- 卡片头部：苗场名称和状态 -->
						<view class="card-header">
							<text class="miaochang-name">{{ record.miaochang.name }}</text>
							<view :class="record.status === 1 ? 'status-pending' : 'status-used'">
								<text class="status-text">{{ record.status === 1 ? '待使用' : '已使用' }}</text>
							</view>
						</view>

						<!-- 用户信息行 -->
						<view class="user-row">
							<view class="user-info">
								<view class="user-avatar">
									<image
										v-if="record.user.avatar"
										:src="record.user.avatar"
										class="avatar-image"
										mode="aspectFill"
									/>
									<view v-else class="avatar-placeholder">
										<text class="avatar-text">{{ getUserAvatarText(record.user) }}</text>
									</view>
								</view>
								<text class="user-name">{{ record.user.nickname }}</text>
							</view>

							<!-- 预种人信息 -->
							<view class="preseed-person-info">
								<template v-if="record.preSeedPerson">
									<view class="preseed-avatar">
										<image
											v-if="record.preSeedPerson.avatar"
											:src="record.preSeedPerson.avatar"
											class="avatar-image"
											mode="aspectFill"
										/>
										<view v-else class="avatar-placeholder">
											<text class="avatar-text">{{ getUserAvatarText(record.preSeedPerson) }}</text>
										</view>
									</view>
									<text class="preseed-name">{{ record.preSeedPerson.nickname }}</text>
								</template>
								<text v-else class="no-preseed-person">暂无预种人</text>
							</view>
						</view>

						<!-- 时间和数量信息 -->
						<view class="info-row">
							<text class="time-text">{{ formatTime(record.preSeedTime) }}</text>
							<text class="quantity-text">数量: {{ record.quantity }}</text>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view v-if="!loading && preseedList.length === 0" class="empty-state">
					<text class="empty-text">暂无预种记录</text>
				</view>

				<!-- 加载更多状态 -->
				<view v-if="loading" class="loading-more">
					<view class="loading-spinner small"></view>
					<text class="loading-text">加载中...</text>
				</view>

				<!-- 没有更多数据 -->
				<view v-if="!hasMore && preseedList.length > 0" class="no-more">
					<text class="no-more-text">没有更多数据了</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { mapGetters } from 'vuex'
import pageEventMixin from '@/mixins/pageEvent.js'
import preseedAPI from '@/api/preseed.js'

export default {
	name: 'PreseedLogsPage',
	mixins: [pageEventMixin],

	data() {
		return {
			preseedList: [],
			loading: false,
			refreshing: false,
			currentPage: 1,
			pageSize: 20,
			hasMore: true,
			totalCount: 0,
			hasLoadedOnce: false     // 是否已经加载过一次（防止重复调用）
		}
	},

	computed: {
		...mapGetters('auth', ['isLoggedIn']),
		...mapGetters('user', ['userInfo']),

		/**
		 * 是否显示管理员按钮
		 * 只有角色为8888或5555的用户才能看到
		 */
		showAdminButton() {
			if (!this.userInfo || !this.userInfo.role) return false
			const roleCode = this.userInfo.role.code || this.userInfo.role
			return roleCode === 8888 || roleCode === 5555
		}
	},

	methods: {
		/**
		 * 获取用户头像文本
		 */
		getUserAvatarText(user) {
			if (!user || !user.nickname) return '?'
			return user.nickname.charAt(0).toUpperCase()
		},

		/**
		 * 格式化时间
		 */
		formatTime(timeStr) {
			if (!timeStr) return '--'
			
			try {
				// 将时间字符串转换为Date对象
				const date = new Date(timeStr.replace(' ', 'T'))
				
				// 格式化为本地时间
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				const hours = String(date.getHours()).padStart(2, '0')
				const minutes = String(date.getMinutes()).padStart(2, '0')
				
				return `${year}-${month}-${day} ${hours}:${minutes}`
			} catch (error) {
				console.error('时间格式化失败:', error)
				return timeStr
			}
		},

		/**
		 * 加载预种记录列表
		 */
		async loadPreseedLogs(refresh = false) {
			// 防止重复调用
			if (this.loading) {
				console.warn('🚫 预种记录正在加载中，跳过重复调用')
				return
			}

			try {
				this.loading = true
				
				if (refresh) {
					this.currentPage = 1
					this.preseedList = []
					this.hasMore = true
				}

				const params = {
					page: this.currentPage,
					page_size: this.pageSize
				}

				console.log('加载预种记录，参数:', params)

				const response = await this.safeNetworkRequest(preseedAPI.getPreseedLogs, params)

				// API直接返回分页数据格式
				if (response && typeof response === 'object' && Array.isArray(response.results)) {
					this.totalCount = response.count || 0
					const newRecords = response.results || []

					if (refresh) {
						this.preseedList = newRecords
					} else {
						this.preseedList = [...this.preseedList, ...newRecords]
					}

					// 检查是否还有更多数据
					this.hasMore = !!response.next

					// 标记已经加载过
					this.hasLoadedOnce = true

					console.log('预种记录加载成功，总数:', this.totalCount)
				} else {
					console.error('❌ API返回格式错误:', response)
					throw new Error('数据格式错误')
				}
			} catch (error) {
				console.error('❌ 加载预种记录失败:', error)
				
				uni.showToast({
					title: error.message || '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
				this.refreshing = false
			}
		},

		/**
		 * 下拉刷新
		 */
		async onRefresh() {
			this.refreshing = true
			await this.loadPreseedLogs(true)
		},

		/**
		 * 加载更多
		 */
		async loadMore() {
			if (!this.hasMore || this.loading) return
			
			this.currentPage++
			await this.loadPreseedLogs(false)
		},

		/**
		 * 重试认证
		 */
		retryAuth() {
			console.log('用户手动重试认证')
			this.authReady = false
			this.authError = null
			this.hasLoadedOnce = false  // 重置加载标志
			this.waitForAuth()
		},

		/**
		 * 跳转到预种子二维码生成页面
		 */
		goToQrCodeGenerate() {
			console.log('🔗 跳转到预种子二维码生成页面')
			console.log('👤 当前用户角色:', this.userInfo?.role)

			uni.navigateTo({
				url: '/pages/PreSeed/boss/generate/qrcode',
				fail: (error) => {
					console.error('❌ 页面跳转失败:', error)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		}
	},

	onLoad() {
		console.log('预种记录页面加载')
	},

	onShow() {
		console.log('📱 页面显示，认证状态:', {
			authReady: this.authReady,
			authSuccess: this.authSuccess,
			dataLength: this.preseedList.length,
			loading: this.loading
		})

		// 如果认证已经完成且成功，但数据还没加载，则手动触发加载
		// 增加 hasLoadedOnce 检查，防止在 onAuthReady 刚调用后立即重复调用
		if (this.authReady && this.authSuccess && this.preseedList.length === 0 && !this.loading && !this.hasLoadedOnce) {
			console.log('检测到认证已完成但数据未加载，手动触发加载')
			this.loadPreseedLogs(true)
		}
	},

	/**
	 * 认证完成回调
	 */
	async onAuthReady(authData) {
		console.log('📋 预种记录页面收到认证完成事件:', authData)
		console.log('🔍 开始加载预种记录数据...')

		try {
			// 加载预种记录数据
			await this.loadPreseedLogs(true)
			console.log('✅ 预种记录数据加载完成')
		} catch (error) {
			console.error('❌ 预种记录数据加载失败:', error)
		}
	},

	/**
	 * 认证失败回调
	 */
	onAuthFailed(errorData) {
		console.log('📋 预种记录页面收到认证失败事件:', errorData)
	}
}
</script>

<style lang="scss" scoped>
.preseed-logs-page {
	height: 100vh;
	background-color: #f5f5f5;
}

/* 认证状态样式 */
.auth-loading, .auth-error {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100vh;
	padding: 40rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #e0e0e0;
	border-top: 4rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

.loading-spinner.small {
	width: 40rpx;
	height: 40rpx;
	border-width: 3rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	color: #666;
	font-size: 28rpx;
}

.error-text {
	color: #ff4757;
	font-size: 28rpx;
	margin-bottom: 20rpx;
	text-align: center;
}

.retry-btn {
	background-color: #007aff;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

/* 页面内容样式 */
.page-content {
	height: 100vh;
}

/* 管理员操作区域样式 */
.admin-section {
	padding: 20rpx;
	background-color: #f8f9fa;
	border-bottom: 1rpx solid #e9ecef;
}

.admin-btn {
	width: 100%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 24rpx;
	font-size: 32rpx;
	font-weight: bold;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
}

.admin-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.btn-text {
	color: white;
	font-size: 32rpx;
	font-weight: bold;
}

.scroll-container {
	height: 100%;
}

/* 预种记录列表样式 */
.preseed-list {
	padding: 20rpx;
}

.preseed-card {
	background-color: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 卡片头部样式 */
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.miaochang-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.status-pending {
	background-color: #fff3cd;
	border: 1rpx solid #ffeaa7;
	border-radius: 12rpx;
	padding: 8rpx 16rpx;
}

.status-used {
	background-color: #d4edda;
	border: 1rpx solid #c3e6cb;
	border-radius: 12rpx;
	padding: 8rpx 16rpx;
}

.status-text {
	font-size: 24rpx;
	color: #333;
}

/* 用户信息行样式 */
.user-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.user-info, .preseed-person-info {
	display: flex;
	align-items: center;
}

.user-avatar, .preseed-avatar {
	width: 60rpx;
	height: 60rpx;
	margin-right: 16rpx;
}

.avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.avatar-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background-color: #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-text {
	color: #666;
	font-size: 24rpx;
	font-weight: bold;
}

.user-name, .preseed-name {
	font-size: 28rpx;
	color: #333;
}

.no-preseed-person {
	font-size: 26rpx;
	color: #999;
	font-style: italic;
}

/* 信息行样式 */
.info-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.time-text {
	font-size: 26rpx;
	color: #666;
}

.quantity-text {
	font-size: 28rpx;
	color: #007aff;
	font-weight: bold;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
}

.empty-text {
	color: #999;
	font-size: 28rpx;
}

/* 加载更多样式 */
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.loading-more .loading-text {
	margin-left: 16rpx;
}

/* 没有更多数据样式 */
.no-more {
	display: flex;
	justify-content: center;
	padding: 40rpx;
}

.no-more-text {
	color: #999;
	font-size: 26rpx;
}
</style>
