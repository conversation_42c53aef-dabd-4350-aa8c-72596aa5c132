<template>
	<view class="content">
		<!-- 认证等待状态 -->
		<view v-if="!showPageContent" class="auth-waiting">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ pageStatusText }}</text>
			</view>
		</view>

		<!-- 页面主要内容 - 只有认证成功后才显示 -->
		<view v-if="showPageContent" class="main-content">
			<image class="logo" src="/static/logo.png"></image>
			<view class="text-area">
				<text class="title">喵帮微信小程序</text>
			</view>

			<!-- 登录状态显示 -->
			<view class="login-status">
				<view class="status-item">
					<text class="label">登录状态：</text>
					<text class="value" :class="isLoggedIn ? 'success' : 'error'">
						{{ isLoggedIn ? '已登录' : '未登录' }}
					</text>
				</view>

				<view class="status-item" v-if="userInfo">
					<text class="label">用户昵称：</text>
					<text class="value">{{ userInfo.nickname || '未设置' }}</text>
				</view>

				<view class="status-item" v-if="userInfo && userInfo.role">
					<text class="label">用户角色：</text>
					<text class="value">{{ userInfo.role.name }}</text>
				</view>

				<view class="status-item" v-if="lastVerifyTime">
					<text class="label">上次验证：</text>
					<text class="value">{{ formatTime(lastVerifyTime) }}</text>
				</view>
			</view>

			<!-- 功能按钮 -->
			<view class="button-group">
				<button class="btn" @click="testLogin" :disabled="loginLoading">
					{{ loginLoading ? '登录中...' : '重新登录' }}
				</button>

				<button class="btn" @click="clearAuth" v-if="isLoggedIn">
					清除登录信息
				</button>

				<button class="btn" @click="showUserInfo" v-if="isLoggedIn">
					查看用户信息
				</button>
			</view>
		</view>

		<!-- 认证失败状态 -->
		<view v-if="authReady && !authSuccess" class="auth-failed">
			<view class="error-container">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{ authError || '登录验证失败' }}</text>
				<button class="retry-btn" @click="retryAuth">重试</button>
			</view>
		</view>
	</view>
</template>

<script>
import { userInfo } from 'os'
import { mapGetters, mapActions, mapState } from 'vuex'
import pageEventMixin from '@/mixins/pageEvent.js'

export default {
	name: 'IndexPage',
	mixins: [pageEventMixin],
	computed: {
		...mapGetters('auth', ['isTokenValid']),
		...mapGetters('user', ['isAgent', 'isMiaochang', 'isUser', 'userRole']),
		...mapState('auth', ['loginLoading', 'lastVerifyTime', 'isLoggedIn']),
		...mapState('user', ['userInfo'])
	},

	methods: {
		...mapActions('auth', ['silentLogin', 'clearAuth']),

		// 安全的测试登录 - 只有在认证完成后才能执行
		async testLogin() {
			if (!this.canExecutePageLogic) {
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			// 防止重复点击
			if (this.loginLoading) {
				return
			}

			try {
				// 使用安全的网络请求方法
				const result = await this.safeNetworkRequest(this.silentLogin)
				if (result.success) {
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: result.message || '登录失败',
						icon: 'none'
					})
				}
			} catch (error) {
				uni.showToast({
					title: '登录失败',
					icon: 'none'
				})
			}
		},

		// 安全的显示用户信息 - 只有在认证完成后才能执行
		showUserInfo() {
			if (!this.canExecutePageLogic) {
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			uni.showModal({
				title: '用户信息',
				content: JSON.stringify(this.userInfo, null, 2),
				showCancel: false
			})
		},

		// 重试认证
		retryAuth() {
			console.log('🔄 用户手动重试认证')
			this.authReady = false
			this.authSuccess = false
			this.authError = null
			this.showPageContent = false
			this.pageLoading = true
			this.waitForAuth()
		},

		// 获取角色名称
		getRoleName(role) {
			const roleMap = {
				'user': '普通用户',
				'agent': '代理',
				'miaochang': '苗场主'
			}
			return roleMap[role] || '未知'
		},

		// 格式化时间
		formatTime(timestamp) {
			if (!timestamp) return '无'
			const date = new Date(timestamp)
			return date.toLocaleString()
		}
	},

	onLoad() {
		console.log('首页加载')
		// pageEventMixin 会自动设置事件监听
	},

	/**
	 * 认证完成回调 - 由 App.vue 事件触发
	 * 只有认证成功后才会执行
	 */
	onAuthReady(authData) {
		console.log('🏠 首页收到认证完成事件:', authData)
		console.log('✅ 首页现在可以安全地加载数据和显示内容')

		// 在这里可以安全地加载需要登录后才能获取的数据
		this.loadPageData()
	},

	/**
	 * 认证失败回调 - 由 App.vue 事件触发
	 */
	onAuthFailed(errorData) {
		console.log('🏠 首页收到认证失败事件:', errorData)
		console.log('❌ 首页无法加载数据，显示错误状态')

		// 可以在这里处理认证失败的情况
		// 页面内容已经被 mixin 隐藏，显示错误状态
	},

	/**
	 * 加载页面数据 - 只有在认证成功后才会被调用
	 */
	async loadPageData() {
		console.log('📊 开始加载首页数据')

		try {
			// 这里可以安全地发起网络请求
			// 例如：加载用户统计数据、公告等

			// 示例：使用安全的网络请求方法
			// const userData = await this.safeNetworkRequest(someAPI.getUserData)

			console.log('✅ 首页数据加载完成')
		} catch (error) {
			console.error('❌ 首页数据加载失败:', error)
			uni.showToast({
				title: '数据加载失败',
				icon: 'none'
			})
		}
	}
}
</script>

<style>
.content {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.logo {
	height: 120rpx;
	width: 120rpx;
	margin-bottom: 30rpx;
}

.text-area {
	margin-bottom: 60rpx;
}

.title {
	font-size: 48rpx;
	color: #333;
	font-weight: bold;
}

.login-status {
	width: 100%;
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.status-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.status-item:last-child {
	margin-bottom: 0;
}

.label {
	font-size: 28rpx;
	color: #666;
	width: 160rpx;
}

.value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.value.success {
	color: #4cd964;
}

.value.error {
	color: #dd524d;
}

.button-group {
	width: 100%;
}

.btn {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 44rpx;
	font-size: 32rpx;
	margin-bottom: 20rpx;
	border: none;
}

.btn:last-child {
	margin-bottom: 0;
}

.btn[disabled] {
	background-color: #c0c0c0;
}

/* 认证等待状态样式 */
.auth-waiting {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid #e0e0e0;
	border-top: 6rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

/* 认证失败状态样式 */
.auth-failed {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
	background-color: #fff;
	border-radius: 20rpx;
	margin: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}

.error-text {
	font-size: 28rpx;
	color: #dd524d;
	text-align: center;
	margin-bottom: 40rpx;
}

.retry-btn {
	width: 200rpx;
	height: 70rpx;
	line-height: 70rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: none;
}

/* 主要内容样式调整 */
.main-content {
	min-height: 100vh;
}
</style>
