/**
 * 代理相关API接口
 */
import { request } from '../utils/request'

export const agentAPI = {
  /**
   * 获取代理列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.page_size - 每页数量
   * @param {string} params.search - 搜索关键词
   * @param {number} params.status - 状态筛选
   * @returns {Promise} API响应
   */
  getAgentList(params = {}) {
    const queryParams = {
      page: 1,
      page_size: 20,
      ...params
    }
    
    // 构建查询字符串
    const queryString = Object.keys(queryParams)
      .filter(key => queryParams[key] !== undefined && queryParams[key] !== null && queryParams[key] !== '')
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
      .join('&')
    
    const url = queryString ? `/api/miaochang/agent/list/?${queryString}` : '/api/miaochang/agent/list/'
    
    console.log('📋 获取代理列表，URL:', url)
    console.log('📋 查询参数:', queryParams)
    
    return request.get(url)
  },

  /**
   * 更新代理信息
   * @param {Object} data - 更新数据
   * @param {number} data.id - 代理ID
   * @param {string} data.nickname - 昵称
   * @param {string} data.phone - 电话
   * @param {string} data.remark - 备注
   * @returns {Promise} API响应
   */
  updateAgent(data) {
    console.log('📝 更新代理信息，数据:', data)

    return request.post('/api/miaochang/agent/update/', {
      id: data.id,
      nickname: data.nickname,
      phone: data.phone,
      remark: data.remark
    })
  },

  /**
   * 更新代理备注 (保留兼容性)
   * @param {number} agentId - 代理ID
   * @param {string} remark - 备注内容
   * @returns {Promise} API响应
   */
  updateAgentRemark(agentId, remark) {
    console.log('📝 更新代理备注，ID:', agentId, '备注:', remark)

    return request.patch(`/api/miaochang/agent/${agentId}/`, {
      remark_boss: remark
    })
  },

  /**
   * 禁用/启用代理
   * @param {number} agentId - 代理ID
   * @returns {Promise} API响应
   */
  toggleAgentStatus(agentId) {
    console.log('🔄 切换代理状态，ID:', agentId)

    return request.get(`/api/miaochang/agent/disable/${agentId}`)
  },

  /**
   * 更新代理状态 (保留兼容性)
   * @param {number} agentId - 代理ID
   * @param {number} status - 状态值
   * @returns {Promise} API响应
   */
  updateAgentStatus(agentId, status) {
    console.log('🔄 更新代理状态，ID:', agentId, '状态:', status)

    return request.patch(`/api/miaochang/agent/${agentId}/`, {
      status: status
    })
  },

  /**
   * 删除代理
   * @param {number} agentId - 代理ID
   * @returns {Promise} API响应
   */
  deleteAgent(agentId) {
    console.log('🗑️ 删除代理，ID:', agentId)
    
    return request.delete(`/api/miaochang/agent/${agentId}/`)
  }
}

export default agentAPI
