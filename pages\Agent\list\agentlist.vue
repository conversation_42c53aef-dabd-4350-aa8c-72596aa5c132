<template>
	<view class="container">
		<!-- 认证等待状态 -->
		<view v-if="!showPageContent" class="auth-waiting">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ pageStatusText }}</text>
			</view>
		</view>

		<!-- 页面主要内容 -->
		<view v-if="showPageContent" class="main-content">
			<!-- 搜索栏 -->
			<view class="search-section">
				<view class="search-container">
					<input
						class="search-input"
						type="text"
						v-model="searchKeyword"
						placeholder="搜索代理昵称或电话"
						@confirm="handleSearch"
						@input="onSearchInput"
					/>
					<button class="search-btn" @click="handleSearch">搜索</button>
				</view>
				<view class="filter-container">
					<picker
						:value="statusFilterIndex"
						:range="statusFilterOptions"
						range-key="label"
						@change="onStatusFilterChange"
					>
						<view class="filter-btn">
							<text>{{ statusFilterOptions[statusFilterIndex].label }}</text>
							<text class="filter-arrow">▼</text>
						</view>
					</picker>
				</view>
			</view>

			<!-- 统计信息 -->
			<view v-if="totalCount > 0" class="stats-section">
				<text class="stats-text">共找到 {{ totalCount }} 个代理</text>
			</view>

			<!-- 代理列表 -->
			<view class="agent-list">
				<view
					v-for="agent in agentList"
					:key="agent.id"
					class="agent-card"
					@click="handleAgentClick(agent)"
				>
					<!-- 代理基本信息 -->
					<view class="agent-header">
						<view class="agent-avatar">
							<image
								v-if="agent.agent.avatar"
								:src="agent.agent.avatar"
								class="avatar-image"
								mode="aspectFill"
							/>
							<view v-else class="avatar-placeholder">
								<text class="avatar-text">{{ getAvatarText(agent) }}</text>
							</view>
						</view>
						<view class="agent-info">
							<view class="agent-name">
								<text class="name-text">{{ getAgentName(agent) }}</text>
								<view :class="agent.status === 1 ? 'status-active' : 'status-inactive'">
									<text class="status-text">{{ getStatusText(agent.status) }}</text>
								</view>
							</view>
							<text v-if="agent.phone" class="agent-phone">{{ formatPhone(agent.phone) }}</text>
							<text v-if="agent.miaochang" class="agent-miaochang">{{ agent.miaochang.name }}</text>
						</view>
					</view>

					<!-- 代理详细信息 -->
					<view class="agent-details">
						<view class="detail-row">
							<text class="detail-label">创建时间:</text>
							<text class="detail-value">{{ formatTime(agent.createTime) }}</text>
						</view>
						<view v-if="agent.remark_boss" class="detail-row">
							<text class="detail-label">备注:</text>
							<text class="detail-value">{{ agent.remark_boss }}</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="agent-actions">
						<button class="action-btn edit-btn" @click.stop="showEditDialog(agent)">
							编辑信息
						</button>
						<button
							:class="'action-btn ' + (agent.status === 1 ? 'disable-btn' : 'enable-btn')"
							@click.stop="toggleAgentStatus(agent)"
						>
							{{ agent.status === 1 ? '禁用' : '启用' }}
						</button>
					</view>
				</view>
			</view>

			<!-- 加载状态 -->
			<view v-if="loading && agentList.length > 0" class="loading-more">
				<view class="loading-spinner small"></view>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-if="!hasMore && agentList.length > 0" class="no-more">
				<text class="no-more-text">没有更多数据了</text>
			</view>

			<!-- 空状态 -->
			<view v-if="!loading && agentList.length === 0" class="empty-state">
				<text class="empty-icon">👥</text>
				<text class="empty-text">暂无代理数据</text>
				<button class="retry-btn" @click="loadAgentList(true)">重新加载</button>
			</view>
		</view>

		<!-- 认证失败状态 -->
		<view v-if="authReady && !authSuccess" class="auth-failed">
			<view class="error-container">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{ authError || '权限验证失败' }}</text>
				<text class="error-desc">无法访问代理列表，请重新登录</text>
				<button class="retry-btn" @click="goBack">返回首页</button>
			</view>
		</view>

		<!-- 代理信息编辑弹窗 -->
		<view v-if="showEditModal" class="modal-overlay" @click="hideEditDialog">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">编辑代理信息</text>
					<text class="modal-close" @click="hideEditDialog">✕</text>
				</view>
				<view class="modal-body">
					<view class="form-group">
						<text class="form-label">昵称 *</text>
						<input
							class="form-input"
							v-model="editForm.nickname"
							placeholder="请输入昵称"
							maxlength="50"
						/>
					</view>
					<view class="form-group">
						<text class="form-label">电话 *</text>
						<input
							class="form-input"
							v-model="editForm.phone"
							placeholder="请输入电话号码"
							type="number"
							maxlength="11"
						/>
					</view>
					<view class="form-group">
						<text class="form-label">备注</text>
						<textarea
							class="form-textarea"
							v-model="editForm.remark"
							placeholder="请输入备注信息（可选）"
							maxlength="200"
						/>
						<text class="char-count">{{ editForm.remark.length }}/200</text>
					</view>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="hideEditDialog">取消</button>
					<button class="modal-btn confirm-btn" @click="saveAgentInfo">保存</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import agentAPI from '../../../api/agent'
import { mapActions, mapGetters } from 'vuex'
import pageEventMixin from '@/mixins/pageEvent.js'

export default {
	name: 'AgentListPage',
	mixins: [pageEventMixin],

	data() {
		return {
			// 列表数据
			agentList: [],
			totalCount: 0,
			currentPage: 1,
			pageSize: 20,
			hasMore: true,
			loading: false,
			refreshing: false,

			// 搜索和筛选
			searchKeyword: '',
			searchTimer: null,
			statusFilterIndex: 0,
			statusFilterOptions: [
				{ label: '全部状态', value: null },
				{ label: '已启用', value: 1 },
				{ label: '已禁用', value: 0 }
			],

			// 代理信息编辑
			showEditModal: false,
			showRemarkModal: false,
			currentAgent: null,
			editForm: {
				nickname: '',
				phone: '',
				remark: ''
			}
		}
	},

	computed: {
		...mapGetters('user', ['userInfo']),

		// 当前筛选状态值
		currentStatusFilter() {
			return this.statusFilterOptions[this.statusFilterIndex].value
		}
	},

	onLoad(options) {
		console.log('代理列表页面加载，参数:', options)
	},

	onReady() {
		// 页面准备完成
		console.log('代理列表页面准备完成')

		// 临时调试：直接调用API
		try {
			this.debugLoadAgentList()
		} catch (error) {
			console.error('❌ 调试方法调用失败:', error)
		}
	},

	onReachBottom() {
		// 触底加载更多
		if (this.hasMore && !this.loading) {
			this.loadMoreAgents()
		}
	},

	onPullDownRefresh() {
		// 下拉刷新
		this.loadAgentList(true)
	},

	methods: {
		...mapActions('auth', ['clearAuth']),

		/**
		 * 认证完成回调
		 */
		onAuthReady(authData) {
			console.log('📋 代理列表页面收到认证完成事件:', authData)
			
			// 检查权限 - 只有特定角色可以访问
			if (!this.checkPermission()) {
				uni.showModal({
					title: '权限不足',
					content: '您没有权限访问代理列表',
					showCancel: false,
					success: () => {
						this.goBack()
					}
				})
				return
			}

			// 加载代理列表
			this.loadAgentList(true)
		},

		/**
		 * 认证失败回调
		 */
		onAuthFailed(errorData) {
			console.log('📋 代理列表页面收到认证失败事件:', errorData)
		},

		/**
		 * 检查访问权限 - 已移除权限限制
		 */
		checkPermission() {
			// 移除权限检查，允许所有已认证用户访问代理列表
			return true
		},

		/**
		 * 加载代理列表
		 */
		async loadAgentList(refresh = false) {
			if (this.loading) return

			try {
				this.loading = true
				
				if (refresh) {
					this.currentPage = 1
					this.agentList = []
					this.hasMore = true
				}

				const params = {
					page: this.currentPage,
					page_size: this.pageSize
				}

				// 添加搜索条件
				if (this.searchKeyword.trim()) {
					params.search = this.searchKeyword.trim()
				}

				// 添加状态筛选
				if (this.currentStatusFilter !== null) {
					params.status = this.currentStatusFilter
				}

				console.log('📋 加载代理列表，参数:', params)

				const response = await this.safeNetworkRequest(agentAPI.getAgentList, params)

				console.log('📥 代理列表API完整响应:', response)
				console.log('🔍 响应状态码:', response.code)
				console.log('🔍 响应消息:', response.msg)
				console.log('🔍 响应数据:', response.results)

				// API直接返回分页数据格式，不是包装格式
				if (response && typeof response === 'object' && Array.isArray(response.results)) {
					this.totalCount = response.count || 0
					const newAgents = response.results || []

					if (refresh) {
						this.agentList = newAgents
					} else {
						this.agentList = [...this.agentList, ...newAgents]
					}

					// 检查是否还有更多数据
					this.hasMore = !!response.next

					console.log('✅ 代理列表加载成功，总数:', this.totalCount, '当前页:', this.currentPage)
					console.log('📋 代理数据:', newAgents)
				} else {
					console.error('❌ API返回格式错误:', response)
					throw new Error('数据格式错误')
				}
			} catch (error) {
				console.error('❌ 加载代理列表失败:', error)
				
				uni.showToast({
					title: error.message || '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
				
				// 停止下拉刷新
				if (refresh) {
					uni.stopPullDownRefresh()
				}
			}
		},

		/**
		 * 加载更多代理
		 */
		async loadMoreAgents() {
			if (!this.hasMore || this.loading) return

			this.currentPage++
			await this.loadAgentList(false)
		},

		/**
		 * 搜索输入处理
		 */
		onSearchInput() {
			// 防抖处理
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}
			
			this.searchTimer = setTimeout(() => {
				this.handleSearch()
			}, 500)
		},

		/**
		 * 执行搜索
		 */
		handleSearch() {
			console.log('🔍 执行搜索，关键词:', this.searchKeyword)
			this.loadAgentList(true)
		},

		/**
		 * 状态筛选变化
		 */
		onStatusFilterChange(e) {
			this.statusFilterIndex = e.detail.value
			console.log('🔍 状态筛选变化:', this.statusFilterOptions[this.statusFilterIndex])
			this.loadAgentList(true)
		},

		/**
		 * 获取代理名称
		 */
		getAgentName(agent) {
			if (agent.nickname) return agent.nickname
			if (agent.agent && agent.agent.nickname) return agent.agent.nickname
			return `代理 #${agent.agent ? agent.agent.id : agent.id}`
		},

		/**
		 * 获取头像文字
		 */
		getAvatarText(agent) {
			const name = this.getAgentName(agent)
			return name.charAt(0).toUpperCase()
		},

		/**
		 * 格式化手机号
		 */
		formatPhone(phone) {
			if (!phone) return '--'
			const phoneStr = phone.toString()
			if (phoneStr.length === 11) {
				return phoneStr.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
			}
			return phone
		},

		/**
		 * 格式化时间
		 */
		formatTime(timeString) {
			if (!timeString) return '--'
			
			try {
				const date = new Date(timeString.replace(/-/g, '/'))
				return date.toLocaleString('zh-CN', {
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit'
				})
			} catch (error) {
				return timeString
			}
		},



		/**
		 * 获取状态文本
		 */
		getStatusText(status) {
			return status === 1 ? '已启用' : '已禁用'
		},

		/**
		 * 代理卡片点击
		 */
		handleAgentClick(agent) {
			console.log('👤 点击代理:', agent)
			// 可以跳转到代理详情页面
		},

		/**
		 * 显示备注编辑弹窗
		 */
		showRemarkDialog(agent) {
			this.currentAgent = agent
			this.remarkText = agent.remark_boss || ''
			this.showRemarkModal = true
		},

		/**
		 * 隐藏备注编辑弹窗
		 */
		hideRemarkDialog() {
			this.showRemarkModal = false
			this.currentAgent = null
			this.remarkText = ''
		},

		/**
		 * 保存备注
		 */
		async saveRemark() {
			if (!this.currentAgent) return

			try {
				uni.showLoading({ title: '保存中...' })

				const response = await this.safeNetworkRequest(
					agentAPI.updateAgentRemark,
					this.currentAgent.id,
					this.remarkText
				)

				if (response.code === 200) {
					// 更新本地数据
					const index = this.agentList.findIndex(item => item.id === this.currentAgent.id)
					if (index !== -1) {
						this.agentList[index].remark_boss = this.remarkText
					}

					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})

					this.hideRemarkDialog()
				} else {
					throw new Error(response.msg || '保存失败')
				}
			} catch (error) {
				console.error('保存备注失败:', error)
				uni.showToast({
					title: error.message || '保存失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 显示代理信息编辑弹窗
		 */
		showEditDialog(agent) {
			console.log('🔧 显示编辑弹窗，代理信息:', agent)
			this.currentAgent = agent
			this.editForm = {
				nickname: agent.nickname || '',
				phone: agent.phone || '',
				remark: agent.remark_boss || ''
			}
			console.log('📝 编辑表单数据:', this.editForm)
			this.showEditModal = true
			console.log('✅ 弹窗状态已设置为true:', this.showEditModal)
		},

		/**
		 * 隐藏代理信息编辑弹窗
		 */
		hideEditDialog() {
			this.showEditModal = false
			this.currentAgent = null
			this.editForm = {
				nickname: '',
				phone: '',
				remark: ''
			}
		},

		/**
		 * 保存代理信息
		 */
		async saveAgentInfo() {
			if (!this.currentAgent) return

			// 验证必填字段
			if (!this.editForm.nickname.trim()) {
				uni.showToast({
					title: '请输入昵称',
					icon: 'none'
				})
				return
			}

			if (!this.editForm.phone.trim()) {
				uni.showToast({
					title: '请输入电话号码',
					icon: 'none'
				})
				return
			}

			try {
				uni.showLoading({ title: '保存中...' })

				const response = await this.safeNetworkRequest(
					agentAPI.updateAgent,
					{
						id: this.currentAgent.id,
						nickname: this.editForm.nickname.trim(),
						phone: this.editForm.phone.trim(),
						remark: this.editForm.remark.trim()
					}
				)

				console.log('📥 更新代理API响应:', response)

				// API返回标准格式 {code, msg, results}
				if (response && response.code === 200) {
					// 更新本地数据 - 使用返回的代理数据
					const updatedAgent = response.results
					if (updatedAgent && updatedAgent.id) {
						const index = this.agentList.findIndex(item => item.id === this.currentAgent.id)
						if (index !== -1) {
							this.agentList[index] = updatedAgent
						}
					}

					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})

					this.hideEditDialog()

					// 刷新列表
					this.loadAgentList(true)
				} else {
					throw new Error(response.msg || '保存失败')
				}
			} catch (error) {
				console.error('保存代理信息失败:', error)
				uni.showToast({
					title: error.message || '保存失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 切换代理状态
		 */
		async toggleAgentStatus(agent) {
			const newStatus = agent.status === 1 ? 0 : 1
			const actionText = newStatus === 1 ? '启用' : '禁用'

			try {
				const confirmRes = await new Promise((resolve) => {
					uni.showModal({
						title: '确认操作',
						content: `确定要${actionText}该代理吗？`,
						success: resolve
					})
				})

				if (!confirmRes.confirm) return

				uni.showLoading({ title: `${actionText}中...` })

				const response = await this.safeNetworkRequest(
					agentAPI.toggleAgentStatus,
					agent.id
				)

				console.log('📥 切换状态API响应:', response)

				// API返回标准格式 {code, msg, results}
				if (response && response.code === 200) {
					// 更新本地数据 - 使用返回的代理数据
					const updatedAgent = response.results
					if (updatedAgent && updatedAgent.id) {
						const index = this.agentList.findIndex(item => item.id === agent.id)
						if (index !== -1) {
							this.agentList[index] = updatedAgent
						}
					}

					uni.showToast({
						title: `${actionText}成功`,
						icon: 'success'
					})

					// 刷新列表以确保数据同步
					this.loadAgentList(true)
				} else {
					throw new Error(response.msg || `${actionText}失败`)
				}
			} catch (error) {
				console.error(`${actionText}代理失败:`, error)
				uni.showToast({
					title: error.message || `${actionText}失败`,
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 调试方法：直接调用API
		 */
		debugLoadAgentList() {
			console.log('🔧 调试：直接调用代理列表API')
			console.log('🔍 agentAPI对象:', agentAPI)

			if (!agentAPI) {
				console.error('❌ agentAPI未正确导入')
				return
			}

			if (!agentAPI.getAgentList) {
				console.error('❌ getAgentList方法不存在')
				return
			}

			console.log('✅ 开始调用API')
			agentAPI.getAgentList({ page: 1, page_size: 10 })
				.then(response => {
					console.log('📥 API响应:', response)
				})
				.catch(error => {
					console.error('❌ API调用失败:', error)
				})
		},

		/**
		 * 返回首页
		 */
		goBack() {
			uni.reLaunch({
				url: '/pages/index/index'
			})
		}
	}
}
</script>

<style>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 认证等待状态样式 */
.auth-waiting {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid rgba(255, 255, 255, 0.3);
	border-top: 6rpx solid #fff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

.loading-spinner.small {
	width: 40rpx;
	height: 40rpx;
	border-width: 4rpx;
	margin-bottom: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	color: rgba(255, 255, 255, 0.9);
	font-size: 28rpx;
	text-align: center;
}

/* 页面主要内容样式 */
.main-content {
	padding: 20rpx;
}

/* 搜索栏样式 */
.search-section {
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.search-container {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.search-input {
	flex: 1;
	height: 70rpx;
	padding: 0 20rpx;
	background-color: #f8f9fa;
	border: 1rpx solid #e9ecef;
	border-radius: 35rpx;
	font-size: 28rpx;
	margin-right: 20rpx;
}

.search-btn {
	width: 120rpx;
	height: 70rpx;
	line-height: 70rpx;
	background-color: #667eea;
	color: #fff;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: none;
	text-align: center;
}

.filter-container {
	display: flex;
	justify-content: flex-start;
}

.filter-btn {
	display: flex;
	align-items: center;
	padding: 15rpx 25rpx;
	background-color: #f8f9fa;
	border: 1rpx solid #e9ecef;
	border-radius: 25rpx;
	font-size: 26rpx;
	color: #666;
}

.filter-arrow {
	margin-left: 10rpx;
	font-size: 20rpx;
}

/* 统计信息样式 */
.stats-section {
	padding: 20rpx 30rpx;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 12rpx;
	margin-bottom: 20rpx;
}

.stats-text {
	font-size: 26rpx;
	color: #666;
}

/* 代理列表样式 */
.agent-list {
	margin-bottom: 40rpx;
}

.agent-card {
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 代理头部信息 */
.agent-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.agent-avatar {
	width: 100rpx;
	height: 100rpx;
	margin-right: 20rpx;
}

.avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.avatar-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background-color: #667eea;
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-text {
	color: #fff;
	font-size: 36rpx;
	font-weight: bold;
}

.agent-info {
	flex: 1;
}

.agent-name {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.name-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-right: 15rpx;
}

.status-active {
	padding: 4rpx 12rpx;
	background-color: #28a745;
	border-radius: 12rpx;
}

.status-inactive {
	padding: 4rpx 12rpx;
	background-color: #dc3545;
	border-radius: 12rpx;
}

.status-text {
	font-size: 20rpx;
	color: #fff;
}

.agent-phone, .agent-miaochang {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 5rpx;
}

/* 代理详细信息 */
.agent-details {
	margin-bottom: 20rpx;
}

.detail-row {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.detail-label {
	font-size: 24rpx;
	color: #666;
	margin-right: 15rpx;
	min-width: 120rpx;
}

.detail-value {
	font-size: 24rpx;
	color: #333;
	flex: 1;
}

/* 操作按钮 */
.agent-actions {
	display: flex;
	gap: 15rpx;
}

.action-btn {
	flex: 1;
	height: 60rpx;
	line-height: 60rpx;
	font-size: 26rpx;
	border-radius: 30rpx;
	border: none;
	text-align: center;
}

.remark-btn,
.edit-btn {
	background-color: #6c757d;
	color: #fff;
}

.enable-btn {
	background-color: #28a745;
	color: #fff;
}

.disable-btn {
	background-color: #dc3545;
	color: #fff;
}

/* 加载更多样式 */
.loading-more {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx;
}

.no-more {
	display: flex;
	justify-content: center;
	padding: 40rpx;
}

.no-more-text {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.7);
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 100rpx 40rpx;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 16rpx;
}

.empty-icon {
	font-size: 100rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 40rpx;
}

.retry-btn {
	width: 200rpx;
	height: 70rpx;
	line-height: 70rpx;
	background-color: #667eea;
	color: #fff;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: none;
}

/* 认证失败状态样式 */
.auth-failed {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
}

.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	margin: 40rpx;
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}

.error-text {
	font-size: 32rpx;
	color: #dd524d;
	font-weight: bold;
	margin-bottom: 20rpx;
	text-align: center;
}

.error-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 40rpx;
	text-align: center;
}

/* 备注编辑弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	width: 600rpx;
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 36rpx;
	color: #999;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-body {
	padding: 30rpx;
}

.remark-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 20rpx;
	border: 1rpx solid #e9ecef;
	border-radius: 8rpx;
	font-size: 28rpx;
	line-height: 1.5;
	box-sizing: border-box;
}

.char-count {
	display: block;
	text-align: right;
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

.modal-footer {
	display: flex;
	border-top: 1rpx solid #e9ecef;
}

.modal-btn {
	flex: 1;
	height: 88rpx;
	line-height: 88rpx;
	font-size: 32rpx;
	border: none;
	text-align: center;
}

.cancel-btn {
	background-color: #f8f9fa;
	color: #666;
	border-right: 1rpx solid #e9ecef;
}

.confirm-btn {
	background-color: #667eea;
	color: #fff;
}

/* 表单样式 */
.form-group {
	margin-bottom: 30rpx;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
	font-weight: 500;
}

.form-input {
	width: 100%;
	height: 80rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	font-size: 28rpx;
	background-color: #fff;
	box-sizing: border-box;
}

.form-input:focus {
	border-color: #667eea;
	outline: none;
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	padding: 20rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	font-size: 28rpx;
	background-color: #fff;
	box-sizing: border-box;
	resize: none;
}

.form-textarea:focus {
	border-color: #667eea;
	outline: none;
}
</style>
