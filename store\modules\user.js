import { USER_TYPES } from '../types'

const userModule = {
  namespaced: true,

  state: {
    userInfo: null // userInfo包含role属性: { id, nickname, avatar, phone, openid, role }
  },

  mutations: {
    [USER_TYPES.SET_USER_INFO](state, userInfo) {
      state.userInfo = userInfo
    },

    [USER_TYPES.CLEAR_USER_INFO](state) {
      state.userInfo = null
    }
  },

  actions: {
    setUserInfo({ commit }, userInfo) {
      commit(USER_TYPES.SET_USER_INFO, userInfo)
    },

    clearUserInfo({ commit }) {
      commit(USER_TYPES.CLEAR_USER_INFO)
    }
  },

  getters: {
    // 用户信息
    userInfo: (state) => state.userInfo,

    // 从userInfo.role获取角色信息
    userRole: (state) => state.userInfo?.role || null,
    isAgent: (state) => state.userInfo?.role === 'agent',
    isMiaochang: (state) => state.userInfo?.role === 'miaochang',
    isUser: (state) => state.userInfo?.role === 'user',
    userName: (state) => state.userInfo?.nickname || '',
    userId: (state) => state.userInfo?.id || null
  }
}

export default userModule
