/**
 * API数据格式适配器
 * 统一处理后端返回的数据格式
 */

/**
 * 适配认证相关API的返回数据
 * @param {object} response API响应数据
 * @returns {object} 标准化的认证数据
 */
export function adaptAuthResponse(response) {
  console.log('🔍 认证响应原始数据:', JSON.stringify(response, null, 2))

  if (!response || response.code !== 200) {
    throw new Error(response?.msg || 'API请求失败')
  }

  const results = response.results || {}
  console.log('🔍 认证results数据:', JSON.stringify(results, null, 2))

  // 检查两种可能的数据格式
  let token, refresh, user

  if (results.jwtToken) {
    // 格式1: {jwtToken: {token, refresh}, user}
    console.log('📋 使用jwtToken格式解析认证数据')
    const { jwtToken } = results
    token = jwtToken.token
    refresh = jwtToken.refresh
    user = results.user
  } else if (results.token) {
    // 格式2: {token, refresh, user}
    console.log('📋 使用直接token格式解析认证数据')
    token = results.token
    refresh = results.refresh
    user = results.user
  } else {
    throw new Error('API返回数据格式错误：找不到token信息')
  }

  if (!token || !refresh) {
    throw new Error('API返回数据格式错误：缺少token或refresh')
  }

  if (!user) {
    throw new Error('API返回数据格式错误：缺少用户信息')
  }

  console.log('✅ 认证数据解析成功')
  return {
    token,
    refresh,
    user,
    originalResponse: response
  }
}

/**
 * 验证用户数据格式
 * @param {object} user 用户数据
 * @returns {boolean} 是否有效
 */
export function validateUserData(user) {
  console.log('🔍 验证用户数据:', JSON.stringify(user, null, 2))

  if (!user || typeof user !== 'object') {
    console.warn('❌ 用户数据无效：不是对象')
    return false
  }

  // 检查必需字段
  const requiredFields = ['id']
  for (const field of requiredFields) {
    if (!(field in user) || user[field] === null || user[field] === undefined) {
      console.warn(`❌ 用户数据缺少必需字段: ${field}`)
      return false
    }
  }

  // 角色字段是可选的，但如果存在应该是有效的
  if (user.role !== undefined && user.role !== null) {
    const validRoles = ['user', 'agent', 'miaochang']
    if (typeof user.role === 'string' && !validRoles.includes(user.role)) {
      console.warn(`⚠️ 用户角色可能无效: ${user.role}，但继续处理`)
      // 不返回false，只是警告
    }
  }

  console.log('✅ 用户数据验证通过')
  return true
}

/**
 * 标准化用户数据
 * @param {object} user 原始用户数据
 * @returns {object} 标准化的用户数据
 */
export function normalizeUserData(user) {
  console.log('🔧 标准化用户数据:', JSON.stringify(user, null, 2))

  if (!validateUserData(user)) {
    throw new Error('用户数据格式无效')
  }

  // 处理角色数据 - 支持嵌套的role对象
  let normalizedRole = user.role
  if (user.role && typeof user.role === 'object') {
    // 如果role是对象，提取其中的信息
    normalizedRole = {
      id: user.role.id,
      name: user.role.name || user.role.role_name || '',
      type: user.role.type || user.role.role_type || '',
      ...user.role
    }
  }

  const normalized = {
    id: user.id,
    nickname: user.nickname || user.nick_name || '',
    avatar: user.avatar || user.avatar_url || '',
    phone: user.phone || user.phone_number || '',
    openid: user.openid || user.open_id || '',
    role: normalizedRole,
    // 保留原始数据的其他字段
    ...user
  }

  console.log('✅ 用户数据标准化完成:', JSON.stringify(normalized, null, 2))
  return normalized
}

/**
 * 适配微信登录响应
 * @param {object} response 微信登录API响应
 * @returns {object} 适配后的数据
 */
export function adaptWechatLoginResponse(response) {
  try {
    const authData = adaptAuthResponse(response)
    const normalizedUser = normalizeUserData(authData.user)
    
    return {
      success: true,
      token: authData.token,
      refresh: authData.refresh,
      user: normalizedUser,
      message: response.msg || '登录成功'
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      originalResponse: response
    }
  }
}

/**
 * 适配JWT验证响应
 * @param {object} response JWT验证API响应
 * @returns {object} 适配后的数据
 */
export function adaptJwtVerifyResponse(response) {
  try {
    console.log('🔍 JWT验证响应原始数据:', JSON.stringify(response, null, 2))

    if (!response || response.code !== 200) {
      throw new Error(response?.msg || 'JWT验证失败')
    }

    const results = response.results || {}
    console.log('🔍 JWT验证results数据:', JSON.stringify(results, null, 2))

    // 检查两种可能的数据格式
    let token, refresh, user

    if (results.jwtToken) {
      // 格式1: {jwtToken: {token, refresh}, user}
      console.log('📋 使用jwtToken格式解析')
      token = results.jwtToken.token
      refresh = results.jwtToken.refresh
      user = results.user
    } else if (results.token) {
      // 格式2: {token, refresh, user}
      console.log('📋 使用直接token格式解析')
      token = results.token
      refresh = results.refresh
      user = results.user
    } else {
      throw new Error('JWT验证响应格式错误：找不到token信息')
    }

    if (!token || !refresh) {
      throw new Error('JWT验证响应格式错误：缺少token或refresh')
    }

    if (!user) {
      throw new Error('JWT验证响应格式错误：缺少用户信息')
    }

    const normalizedUser = normalizeUserData(user)
    console.log('✅ JWT验证数据解析成功')

    return {
      success: true,
      token: token,
      refresh: refresh,
      user: normalizedUser,
      message: response.msg || '验证成功'
    }
  } catch (error) {
    console.error('❌ JWT验证响应适配失败:', error.message)
    console.error('📄 原始响应:', JSON.stringify(response, null, 2))
    return {
      success: false,
      error: error.message,
      originalResponse: response
    }
  }
}

/**
 * 通用API响应适配器
 * @param {object} response API响应
 * @returns {object} 适配后的数据
 */
export function adaptApiResponse(response) {
  if (!response) {
    return {
      success: false,
      error: '响应数据为空'
    }
  }
  
  return {
    success: response.code === 200,
    code: response.code,
    message: response.msg || '',
    data: response.results || null,
    originalResponse: response
  }
}
