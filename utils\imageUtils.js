/**
 * 图片处理工具函数
 * 支持图片压缩、格式转换、base64编码等功能
 */

/**
 * 选择图片并转换为base64
 * @param {object} options 选项
 * @param {number} options.maxWidth 最大宽度，默认800
 * @param {number} options.maxHeight 最大高度，默认800
 * @param {number} options.quality 压缩质量，0-1，默认0.8
 * @param {string} options.sourceType 图片来源，'album'|'camera'，默认'album'
 * @returns {Promise<string>} base64格式的图片数据
 */
export function chooseImageToBase64(options = {}) {
  const {
    maxWidth = 800,
    maxHeight = 800,
    quality = 0.8,
    sourceType = 'album'
  } = options

  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: [sourceType],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        console.log('📷 选择图片成功:', tempFilePath)
        
        // 压缩并转换为base64
        compressImageToBase64(tempFilePath, {
          maxWidth,
          maxHeight,
          quality
        }).then(resolve).catch(reject)
      },
      fail: (error) => {
        console.error('📷 选择图片失败:', error)
        reject(new Error('选择图片失败'))
      }
    })
  })
}

/**
 * 压缩图片并转换为base64
 * @param {string} filePath 图片文件路径
 * @param {object} options 压缩选项
 * @param {number} options.maxWidth 最大宽度
 * @param {number} options.maxHeight 最大高度
 * @param {number} options.quality 压缩质量
 * @returns {Promise<string>} base64格式的图片数据
 */
export function compressImageToBase64(filePath, options = {}) {
  const {
    maxWidth = 800,
    maxHeight = 800,
    quality = 0.8
  } = options

  return new Promise((resolve, reject) => {
    // 获取图片信息
    uni.getImageInfo({
      src: filePath,
      success: (imageInfo) => {
        console.log('📊 图片信息:', imageInfo)
        
        // 计算压缩后的尺寸
        const { width, height } = calculateCompressSize(
          imageInfo.width,
          imageInfo.height,
          maxWidth,
          maxHeight
        )
        
        console.log(`🔧 压缩尺寸: ${imageInfo.width}x${imageInfo.height} → ${width}x${height}`)
        
        // 创建canvas进行压缩
        const canvas = uni.createCanvasContext('imageCanvas')
        
        // 绘制压缩后的图片
        canvas.drawImage(filePath, 0, 0, width, height)
        canvas.draw(false, () => {
          // 导出为base64
          uni.canvasToTempFilePath({
            canvasId: 'imageCanvas',
            width: width,
            height: height,
            destWidth: width,
            destHeight: height,
            quality: quality,
            fileType: 'jpg',
            success: (canvasRes) => {
              // 将临时文件转换为base64
              fileToBase64(canvasRes.tempFilePath)
                .then(resolve)
                .catch(reject)
            },
            fail: (error) => {
              console.error('🎨 Canvas导出失败:', error)
              // 降级方案：直接转换原图
              fileToBase64(filePath)
                .then(resolve)
                .catch(reject)
            }
          })
        })
      },
      fail: (error) => {
        console.error('📊 获取图片信息失败:', error)
        // 降级方案：直接转换
        fileToBase64(filePath)
          .then(resolve)
          .catch(reject)
      }
    })
  })
}

/**
 * 文件转base64
 * @param {string} filePath 文件路径
 * @returns {Promise<string>} base64数据
 */
export function fileToBase64(filePath) {
  return new Promise((resolve, reject) => {
    uni.getFileSystemManager().readFile({
      filePath: filePath,
      encoding: 'base64',
      success: (res) => {
        // 添加数据URL前缀
        const base64 = `data:image/jpeg;base64,${res.data}`
        console.log('✅ 文件转base64成功，大小:', formatFileSize(base64.length))
        resolve(base64)
      },
      fail: (error) => {
        console.error('📄 文件转base64失败:', error)
        reject(new Error('文件转换失败'))
      }
    })
  })
}

/**
 * 计算压缩后的尺寸
 * @param {number} originalWidth 原始宽度
 * @param {number} originalHeight 原始高度
 * @param {number} maxWidth 最大宽度
 * @param {number} maxHeight 最大高度
 * @returns {object} 压缩后的尺寸 {width, height}
 */
export function calculateCompressSize(originalWidth, originalHeight, maxWidth, maxHeight) {
  let { width, height } = { width: originalWidth, height: originalHeight }
  
  // 如果图片尺寸小于最大限制，不需要压缩
  if (width <= maxWidth && height <= maxHeight) {
    return { width, height }
  }
  
  // 计算缩放比例
  const widthRatio = maxWidth / width
  const heightRatio = maxHeight / height
  const ratio = Math.min(widthRatio, heightRatio)
  
  // 应用缩放比例
  width = Math.floor(width * ratio)
  height = Math.floor(height * ratio)
  
  return { width, height }
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 验证base64图片格式
 * @param {string} base64 base64数据
 * @returns {boolean} 是否有效
 */
export function validateBase64Image(base64) {
  if (!base64 || typeof base64 !== 'string') {
    return false
  }
  
  // 检查是否是有效的base64图片格式
  const imageRegex = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/
  return imageRegex.test(base64)
}

/**
 * 获取base64图片的大小信息
 * @param {string} base64 base64数据
 * @returns {object} 大小信息
 */
export function getBase64ImageSize(base64) {
  if (!validateBase64Image(base64)) {
    throw new Error('无效的base64图片格式')
  }
  
  // 计算base64数据大小
  const base64Data = base64.split(',')[1]
  const bytes = Math.ceil(base64Data.length * 0.75) // base64编码后大小约为原始大小的4/3
  
  return {
    bytes: bytes,
    formatted: formatFileSize(bytes),
    base64Length: base64.length
  }
}

/**
 * 压缩base64图片
 * @param {string} base64 原始base64数据
 * @param {object} options 压缩选项
 * @returns {Promise<string>} 压缩后的base64数据
 */
export function compressBase64Image(base64, options = {}) {
  const {
    maxWidth = 800,
    maxHeight = 800,
    quality = 0.8
  } = options
  
  return new Promise((resolve, reject) => {
    if (!validateBase64Image(base64)) {
      reject(new Error('无效的base64图片格式'))
      return
    }
    
    // 创建临时文件
    const tempFilePath = `${wx.env.USER_DATA_PATH}/temp_image_${Date.now()}.jpg`
    const base64Data = base64.split(',')[1]
    
    uni.getFileSystemManager().writeFile({
      filePath: tempFilePath,
      data: base64Data,
      encoding: 'base64',
      success: () => {
        // 压缩临时文件
        compressImageToBase64(tempFilePath, options)
          .then(resolve)
          .catch(reject)
          .finally(() => {
            // 清理临时文件
            uni.getFileSystemManager().unlink({
              filePath: tempFilePath,
              success: () => console.log('🗑️ 临时文件清理成功'),
              fail: (error) => console.warn('🗑️ 临时文件清理失败:', error)
            })
          })
      },
      fail: (error) => {
        console.error('📄 创建临时文件失败:', error)
        reject(new Error('图片处理失败'))
      }
    })
  })
}

/**
 * 图片选择器配置
 */
export const IMAGE_PICKER_CONFIG = {
  // 头像选择配置
  avatar: {
    maxWidth: 400,
    maxHeight: 400,
    quality: 0.8,
    sourceType: 'album'
  },
  
  // 普通图片配置
  normal: {
    maxWidth: 800,
    maxHeight: 800,
    quality: 0.8,
    sourceType: 'album'
  },
  
  // 高质量图片配置
  highQuality: {
    maxWidth: 1200,
    maxHeight: 1200,
    quality: 0.9,
    sourceType: 'album'
  }
}

export default {
  chooseImageToBase64,
  compressImageToBase64,
  fileToBase64,
  calculateCompressSize,
  formatFileSize,
  validateBase64Image,
  getBase64ImageSize,
  compressBase64Image,
  IMAGE_PICKER_CONFIG
}
