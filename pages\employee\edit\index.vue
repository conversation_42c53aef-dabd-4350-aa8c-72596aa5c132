<template>
	<view class="employee-edit-page">
		<!-- 认证等待状态 -->
		<view v-if="!authReady" class="auth-loading">
			<view class="loading-spinner"></view>
			<text class="loading-text">正在验证身份...</text>
		</view>

		<!-- 认证失败状态 -->
		<view v-else-if="authError" class="auth-error">
			<text class="error-text">{{ authError }}</text>
			<button @click="retryAuth" class="retry-btn">重试</button>
		</view>

		<!-- 页面内容 -->
		<view v-else class="page-content">
			<!-- 页面标题 -->
			<view class="page-header">
				<text class="page-title">编辑员工信息</text>
				<text class="page-subtitle">{{ getEmployeeDisplayName() }}</text>
			</view>

			<!-- 头像设置区域 -->
			<view class="avatar-section">
				<view class="section-header">
					<text class="section-title">头像设置</text>
					<text class="section-desc">点击头像使用微信头像</text>
				</view>

				<view class="avatar-container">
					<button
						class="avatar-button"
						open-type="chooseAvatar"
						@chooseavatar="onChooseAvatar"
						:disabled="saving"
					>
						<view class="avatar-wrapper">
							<image
								class="avatar-preview"
								:src="getAvatarSrc()"
								mode="aspectFill"
								@error="onAvatarError"
							/>
							<view class="avatar-overlay">
								<text class="camera-icon">📷</text>
								<text class="change-text">点击更换</text>
							</view>
						</view>
					</button>

					<!-- 头像状态指示 -->
					<view v-if="avatarChanged" class="change-indicator">
						<text class="change-icon">✨</text>
						<text class="change-text">头像已修改</text>
					</view>
				</view>
			</view>

			<!-- 基本信息编辑区域 -->
			<view class="info-section">
				<view class="section-header">
					<text class="section-title">基本信息</text>
				</view>

				<view class="form-container">
					<!-- 昵称编辑 -->
					<view class="form-group">
						<view class="form-label-row">
							<text class="form-label">昵称</text>
							<text class="char-count">{{ getNicknameLength() }}/20</text>
						</view>
						<input
							class="form-input"
							type="nickname"
							v-model="formData.nickname"
							placeholder="请输入昵称"
							maxlength="20"
							:disabled="saving"
							@input="onNicknameInput"
						/>
						<view v-if="nicknameChanged" class="field-changed">
							<text class="change-icon">✨</text>
							<text class="change-text">已修改</text>
						</view>
					</view>

					<!-- 电话编辑 -->
					<view class="form-group">
						<view class="form-label-row">
							<text class="form-label">电话</text>
							<text class="phone-format">格式: 13800138000</text>
						</view>
						<input
							class="form-input"
							type="number"
							v-model="formData.employeePhone"
							placeholder="请输入11位手机号"
							maxlength="11"
							:disabled="saving"
							@input="onPhoneInput"
						/>
						<view v-if="phoneChanged" class="field-changed">
							<text class="change-icon">✨</text>
							<text class="change-text">已修改</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 权限设置区域 -->
			<view class="permissions-section">
				<view class="section-header">
					<text class="section-title">权限设置</text>
					<text class="section-desc">设置员工的系统权限</text>
				</view>

				<view class="permissions-container">
					<view
						v-for="(permission, key) in permissions"
						:key="key"
						class="permission-item"
					>
						<view class="permission-content">
							<view class="permission-main">
								<text class="permission-icon">{{ permission.icon }}</text>
								<view class="permission-info">
									<text class="permission-name">{{ permission.name }}</text>
									<text class="permission-desc">{{ permission.description }}</text>
								</view>
							</view>

							<view class="permission-control">
								<switch
									:checked="permission.value"
									@change="onPermissionChange(key, $event)"
									:disabled="saving"
									color="#007aff"
								/>
							</view>
						</view>

						<view v-if="isPermissionChanged(key)" class="permission-changed">
							<text class="change-icon">✨</text>
							<text class="change-text">权限已修改</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 预览区域 -->
			<view v-if="hasChanges" class="preview-section">
				<view class="section-header">
					<text class="section-title">修改预览</text>
					<text class="section-desc">保存前预览修改内容</text>
				</view>

				<view class="preview-container">
					<view class="preview-card">
						<image
							class="preview-avatar"
							:src="getAvatarSrc()"
							mode="aspectFill"
						/>
						<view class="preview-info">
							<text class="preview-nickname">{{ getPreviewNickname() }}</text>
							<text class="preview-phone">{{ getPreviewPhone() }}</text>
							<text class="preview-changes">{{ getChangeSummary() }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 操作按钮区域 -->
			<view class="action-section">
				<button
					:class="saveButtonClass"
					@click="saveEmployee"
					:disabled="saving || !hasChanges"
				>
					<text v-if="saving">保存中...</text>
					<text v-else-if="hasChanges">保存修改 ({{ getChangeCount() }}项)</text>
					<text v-else>无修改</text>
				</button>

				<button class="cancel-btn" @click="goBack" :disabled="saving">
					取消
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import pageEventMixin from '@/mixins/pageEvent.js'
import { userAPI } from '@/api/user.js'

export default {
	name: 'EmployeeEditPage',
	mixins: [pageEventMixin],

	data() {
		return {
			defaultAvatar: '/static/default-avatar.png',
			employeeData: null,
			formData: {
				nickname: '',
				employeePhone: ''
			},
			currentAvatar: '',
			originalAvatar: '',
			originalNickname: '',
			originalPhone: '',
			avatarChanged: false,
			nicknameChanged: false,
			phoneChanged: false,
			permissions: {},
			originalPermissions: {},
			saving: false,
			// 备份页面参数，防止丢失
			backupPageOptions: null
		}
	},

	computed: {
		/**
		 * 是否有修改
		 */
		hasChanges() {
			return this.avatarChanged || this.nicknameChanged || this.phoneChanged || this.permissionsChanged
		},

		/**
		 * 权限是否改变
		 */
		permissionsChanged() {
			// 检查权限对象是否已初始化
			if (!this.permissions || Object.keys(this.permissions).length === 0) {
				return false
			}

			if (!this.originalPermissions || Object.keys(this.originalPermissions).length === 0) {
				return false
			}

			const currentValues = this.getPermissionValues()
			const originalValues = this.getOriginalPermissionValues()

			return JSON.stringify(currentValues) !== JSON.stringify(originalValues)
		},

		/**
		 * 保存按钮样式类
		 */
		saveButtonClass() {
			return {
				'save-btn': true,
				'save-btn-active': this.hasChanges && !this.saving,
				'save-btn-disabled': !this.hasChanges || this.saving
			}
		}
	},

	watch: {
		'formData.nickname'(newVal) {
			this.nicknameChanged = newVal !== this.originalNickname
		},
		'formData.employeePhone'(newVal) {
			this.phoneChanged = newVal !== this.originalPhone
		}
	},

	methods: {
		/**
		 * 在 onLoad 时解析员工数据
		 */
		parseEmployeeDataFromOptions(options) {
			console.log('🚀 ===== onLoad 时解析员工数据开始 =====')
			console.log('🚀 解析时间:', new Date().toLocaleString())
			console.log('🚀 原始参数:', options)

			if (options && options.employee) {
				try {
					console.log('🔓 开始解析员工数据...')
					this.employeeData = JSON.parse(decodeURIComponent(options.employee))

					console.log('✅ 员工数据解析成功!')
					console.log('📋 完整解析结果:', this.employeeData)
					console.log('📋 数据类型:', typeof this.employeeData)
					console.log('📋 数据键值:', Object.keys(this.employeeData))

					console.log('🆔 ID字段详细分析:')
					console.log('  - id:', this.employeeData.id, typeof this.employeeData.id)
					console.log('  - user_id:', this.employeeData.user_id, typeof this.employeeData.user_id)
					console.log('  - employee_id:', this.employeeData.employee_id, typeof this.employeeData.employee_id)
					console.log('  - userId:', this.employeeData.userId, typeof this.employeeData.userId)
					console.log('  - employeeId:', this.employeeData.employeeId, typeof this.employeeData.employeeId)

					console.log('📱 基本信息:')
					console.log('  - nickname:', this.employeeData.nickname, typeof this.employeeData.nickname)
					console.log('  - employeePhone:', this.employeeData.employeePhone, typeof this.employeeData.employeePhone)
					console.log('  - avatar:', this.employeeData.avatar ? 'exists' : 'null/undefined', typeof this.employeeData.avatar)

					console.log('🔐 角色信息:')
					console.log('  - role:', this.employeeData.role, typeof this.employeeData.role)
					if (this.employeeData.role) {
						console.log('  - role keys:', Object.keys(this.employeeData.role))
						Object.keys(this.employeeData.role).forEach(key => {
							console.log(`    - ${key}:`, this.employeeData.role[key], typeof this.employeeData.role[key])
						})
					}

					// 检查关键字段
					const hasId = this.employeeData.id

					if (!hasId) {
						console.warn('⚠️ 员工数据缺少ID字段，但继续显示编辑页面')
						console.log('📋 完整员工数据:', this.employeeData)
					} else {
						console.log('✅ 找到员工ID:', hasId)
					}

					// 立即初始化表单数据
					this.initFormData()

					// 强制更新视图
					this.$nextTick(() => {
						console.log('🔄 onLoad 解析完成后强制更新视图')
						this.$forceUpdate()
					})

					console.log('🚀 ===== onLoad 时解析员工数据成功 =====')

				} catch (error) {
					console.error('❌ onLoad 时解析员工数据失败:', error)
					console.log('📦 原始数据:', options.employee)
					this.employeeData = null
				}
			} else {
				console.warn('⚠️ onLoad 时缺少 employee 参数')
				console.log('📋 当前参数:', options)
				this.employeeData = null
			}
		},

		/**
		 * 解析页面参数（认证完成后调用）
		 */
		parsePageOptions() {
			console.log('📋 ===== 认证完成后检查数据状态 =====')
			console.log('📋 当前员工数据状态:', !!this.employeeData)
			console.log('📋 检查时间:', new Date().toLocaleString())

			if (this.employeeData) {
				console.log('✅ 员工数据已存在（在onLoad时解析），无需重新解析')
				console.log('� 员工数据:', this.employeeData)

				// 确保表单数据已初始化
				if (!this.formData.nickname && !this.formData.employeePhone && !this.currentAvatar) {
					console.log('🔄 重新初始化表单数据')
					this.initFormData()
				}

				// 强制更新视图
				this.$nextTick(() => {
					console.log('🔄 认证完成后强制更新视图')
					this.$forceUpdate()
				})
			} else {
				console.warn('⚠️ 员工数据不存在，尝试从备份参数重新解析')

				// 优先使用备份参数，如果备份参数不存在则使用当前参数
				const optionsToUse = this.backupPageOptions || this.pageOptions
				console.log('📋 使用的参数:', optionsToUse)

				if (optionsToUse && optionsToUse.employee) {
					try {
						console.log('🔓 从备份参数重新解析员工数据...')
						this.employeeData = JSON.parse(decodeURIComponent(optionsToUse.employee))
						console.log('✅ 重新解析成功:', this.employeeData)

						// 初始化表单数据
						this.initFormData()

						// 强制更新视图
						this.$nextTick(() => {
							this.$forceUpdate()
						})
					} catch (error) {
						console.error('❌ 重新解析失败:', error)
						uni.showToast({
							title: '数据解析失败',
							icon: 'none'
						})
						this.goBack()
					}
				} else {
					console.error('❌ 无法获取员工数据')
					uni.showToast({
						title: '缺少员工数据',
						icon: 'none'
					})
					this.goBack()
				}
			}

			console.log('📋 ===== 认证完成后检查数据状态结束 =====')
		},

		/**
		 * 初始化表单数据
		 */
		initFormData() {
			console.log('📝 ===== 初始化表单数据调试开始 =====')
			console.log('📝 初始化时间:', new Date().toLocaleString())

			if (!this.employeeData) {
				console.error('❌ 员工数据为空，无法初始化表单')
				return
			}

			console.log('📝 开始初始化表单数据...')
			console.log('📝 原始员工数据:', this.employeeData)

			// 基本信息
			this.formData.nickname = this.employeeData.nickname || ''
			this.formData.employeePhone = this.employeeData.employeePhone || ''
			console.log('📝 表单数据初始化:', {
				nickname: this.formData.nickname,
				employeePhone: this.formData.employeePhone
			})

			// 头像处理 - 确保有默认头像
			this.currentAvatar = this.employeeData.avatar || this.defaultAvatar
			console.log('🖼️ 头像初始化:')
			console.log('  - 原始头像:', this.employeeData.avatar)
			console.log('  - 默认头像:', this.defaultAvatar)
			console.log('  - 当前头像:', this.currentAvatar)

			// 保存原始值
			this.originalNickname = this.formData.nickname
			this.originalPhone = this.formData.employeePhone
			this.originalAvatar = this.currentAvatar
			console.log('📝 原始值保存:', {
				originalNickname: this.originalNickname,
				originalPhone: this.originalPhone,
				originalAvatar: this.originalAvatar ? 'exists' : 'empty'
			})

			// 重置变更状态
			this.avatarChanged = false
			this.nicknameChanged = false
			this.phoneChanged = false
			console.log('📝 变更状态重置完成')

			// 初始化权限
			console.log('🔐 开始初始化权限...')
			this.initPermissions()

			console.log('📝 ===== 初始化表单数据调试结束 =====')
		},

		/**
		 * 初始化权限列表
		 */
		initPermissions() {
			console.log('🔐 ===== 权限初始化调试开始 =====')
			console.log('🔐 权限初始化时间:', new Date().toLocaleString())

			// 定义可用权限列表 - 根据实际API返回的权限字段
			const availablePermissions = {
				canCount: {
					name: '计数权限',
					description: '可以进行计数操作',
					icon: '�'
				},
				canMeasure: {
					name: '测量权限',
					description: '可以进行测量操作',
					icon: '�'
				},
				canFeed: {
					name: '喂养权限',
					description: '可以进行喂养操作',
					icon: '🍽️'
				},
				canPreSeed: {
					name: '预播种权限',
					description: '可以进行预播种操作',
					icon: '🌱'
				},
				canSample: {
					name: '取样权限',
					description: '可以进行取样操作',
					icon: '🧪'
				},
				canDocs: {
					name: '文档权限',
					description: '可以查看和管理文档',
					icon: '📄'
				},
				canAccount: {
					name: '账户权限',
					description: '可以管理账户信息',
					icon: '�'
				}
			}
			console.log('🔐 可用权限列表:', Object.keys(availablePermissions))

			// 从员工数据根级别提取权限（不是从role中）
			console.log('🔐 员工完整数据:', this.employeeData)
			console.log('🔐 员工数据类型:', typeof this.employeeData)
			console.log('🔐 员工数据键值:', Object.keys(this.employeeData))

			// 检查权限字段是否在根级别
			console.log('🔐 权限字段检查:')
			Object.keys(availablePermissions).forEach(key => {
				console.log(`  - ${key}:`, this.employeeData[key], typeof this.employeeData[key])
			})

			// 初始化权限状态
			this.permissions = {}
			this.originalPermissions = {}

			console.log('🔐 开始逐个初始化权限...')
			Object.keys(availablePermissions).forEach(key => {
				// 从员工数据根级别获取权限值
				const value = this.employeeData[key] || false
				console.log(`🔐 权限 ${key}:`, {
					原始值: this.employeeData[key],
					最终值: value,
					类型: typeof value
				})

				// 使用Vue.set确保响应式
				this.$set(this.permissions, key, {
					...availablePermissions[key],
					value: value
				})
				this.$set(this.originalPermissions, key, {
					...availablePermissions[key],
					value: value
				})
			})

			console.log('🔐 最终权限配置:', this.permissions)
			console.log('🔐 原始权限备份:', this.originalPermissions)

			// 强制更新视图确保响应式
			this.$nextTick(() => {
				this.$forceUpdate()
				console.log('🔐 权限初始化后强制更新完成')
			})

			console.log('🔐 ===== 权限初始化调试结束 =====')
		},

		/**
		 * 微信头像选择
		 */
		async onChooseAvatar(e) {
			try {
				const { avatarUrl } = e.detail || {}
				console.log('📋 头像选择事件详情:', e.detail)

				if (!avatarUrl) {
					throw new Error('未获取到头像信息，请重试')
				}

				uni.showLoading({
					title: '处理头像中...'
				})

				console.log('📷 获取到微信头像URL:', avatarUrl)

				// 将微信头像转换为base64
				const base64 = await this.convertWechatAvatarToBase64(avatarUrl)

				if (!base64 || !base64.startsWith('data:image/')) {
					throw new Error('头像转换失败，格式无效')
				}

				// 更新状态
				this.currentAvatar = base64
				this.avatarChanged = true

				console.log('✅ 微信头像设置成功')
				uni.showToast({
					title: '头像选择成功',
					icon: 'success'
				})

			} catch (error) {
				console.error('❌ 处理微信头像失败:', error)
				uni.showToast({
					title: error.message || '头像处理失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 转换微信头像为base64
		 */
		async convertWechatAvatarToBase64(avatarUrl) {
			return new Promise((resolve, reject) => {
				uni.downloadFile({
					url: avatarUrl,
					success: (downloadRes) => {
						if (downloadRes.statusCode === 200) {
							uni.getFileSystemManager().readFile({
								filePath: downloadRes.tempFilePath,
								encoding: 'base64',
								success: (readRes) => {
									const base64 = `data:image/jpeg;base64,${readRes.data}`
									resolve(base64)
								},
								fail: (error) => {
									console.error('❌ 读取文件失败:', error)
									reject(new Error('读取头像文件失败'))
								}
							})
						} else {
							reject(new Error('下载头像失败'))
						}
					},
					fail: (error) => {
						console.error('❌ 下载头像失败:', error)
						reject(new Error('下载头像失败'))
					}
				})
			})
		},

		/**
		 * 头像错误处理
		 */
		onAvatarError(e) {
			console.error('❌ 头像加载失败:', e)
			console.log('🔄 当前头像路径:', this.currentAvatar)
			console.log('🔄 切换到默认头像:', this.defaultAvatar)

			// 如果当前头像不是默认头像，则切换到默认头像
			if (this.currentAvatar !== this.defaultAvatar) {
				this.currentAvatar = this.defaultAvatar
				console.log('✅ 已切换到默认头像')
			} else {
				console.error('❌ 默认头像也加载失败')
				// 尝试使用一个简单的占位符
				this.currentAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lpLTlg488L3RleHQ+PC9zdmc+'
			}

			// 强制更新视图
			this.$forceUpdate()
		},

		/**
		 * 权限变更处理
		 */
		onPermissionChange(key, event) {
			const newValue = event.detail.value
			const oldValue = this.permissions[key].value
			console.log(`🔐 权限变更: ${key} = ${oldValue} → ${newValue}`)

			// 更新权限值 - 使用Vue.set确保响应式更新
			this.$set(this.permissions[key], 'value', newValue)

			// 触发响应式更新
			this.$forceUpdate()

			// 检查变更状态
			this.$nextTick(() => {
				console.log('🔐 权限变更后状态检查:')
				console.log('  - 权限对象:', this.permissions)
				console.log('  - 原始权限对象:', this.originalPermissions)
				console.log('  - 权限是否变更:', this.permissionsChanged)
				console.log('  - 是否有修改:', this.hasChanges)
				console.log('  - 当前权限值:', this.getPermissionValues())
				console.log('  - 原始权限值:', this.getOriginalPermissionValues())

				// 强制触发计算属性重新计算
				this.$forceUpdate()
			})
		},

		/**
		 * 检查单个权限是否改变
		 */
		isPermissionChanged(key) {
			return (this.permissions[key] && this.permissions[key].value) !== (this.originalPermissions[key] && this.originalPermissions[key].value)
		},

		/**
		 * 获取当前权限值
		 */
		getPermissionValues() {
			const values = {}
			Object.keys(this.permissions).forEach(key => {
				values[key] = this.permissions[key].value
			})
			return values
		},

		/**
		 * 获取原始权限值
		 */
		getOriginalPermissionValues() {
			console.log('🔐 获取原始权限值 - originalPermissions对象:', this.originalPermissions)
			const values = {}
			Object.keys(this.originalPermissions).forEach(key => {
				const value = this.originalPermissions[key].value
				values[key] = value
				console.log(`🔐 原始权限 ${key}:`, value)
			})
			console.log('🔐 最终原始权限值:', values)
			return values
		},

		/**
		 * 昵称输入处理
		 */
		onNicknameInput(e) {
			this.formData.nickname = e.detail.value
		},

		/**
		 * 电话输入处理
		 */
		onPhoneInput(e) {
			this.formData.employeePhone = e.detail.value
		},

		/**
		 * 获取修改数量
		 */
		getChangeCount() {
			let count = 0
			if (this.avatarChanged) count++
			if (this.nicknameChanged) count++
			if (this.phoneChanged) count++
			if (this.permissionsChanged) count++
			return count
		},

		/**
		 * 获取修改摘要
		 */
		getChangeSummary() {
			const changes = []
			if (this.avatarChanged) changes.push('头像')
			if (this.nicknameChanged) changes.push('昵称')
			if (this.phoneChanged) changes.push('电话')
			if (this.permissionsChanged) changes.push('权限')

			return changes.length > 0 ? `已修改: ${changes.join('、')}` : '无修改'
		},

		/**
		 * 获取员工显示名称
		 */
		getEmployeeDisplayName() {
			if (!this.employeeData) {
				return '员工 (等待加载...)'
			}

			const name = this.employeeData.nickname || '员工'
			const id = this.employeeData.id || '无ID'

			return `${name} (ID: ${id})`
		},

		/**
		 * 获取昵称长度
		 */
		getNicknameLength() {
			return (this.formData.nickname || '').length
		},

		/**
		 * 获取预览昵称
		 */
		getPreviewNickname() {
			return this.formData.nickname || '未设置昵称'
		},

		/**
		 * 获取预览电话
		 */
		getPreviewPhone() {
			return this.formData.employeePhone || '未设置电话'
		},

		/**
		 * 获取头像源
		 */
		getAvatarSrc() {
			// 如果有当前头像且不为空字符串
			if (this.currentAvatar && this.currentAvatar.trim() !== '') {
				// 如果是base64格式，直接返回
				if (this.currentAvatar.startsWith('data:image/')) {
					return this.currentAvatar
				}
				// 如果是URL格式，直接返回
				if (this.currentAvatar.startsWith('http')) {
					return this.currentAvatar
				}
				// 如果是相对路径，也直接返回
				return this.currentAvatar
			}

			// 否则返回默认头像
			return this.defaultAvatar
		},



		/**
		 * 重试认证
		 */
		retryAuth() {
			console.log('🔄 用户手动重试认证')
			this.authReady = false
			this.authError = null
			this.waitForAuth()
		},

		/**
		 * 保存员工信息
		 */
		async saveEmployee() {
			if (!this.canMakeNetworkRequest) {
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			if (this.saving || !this.hasChanges) {
				return
			}

			try {
				this.saving = true
				uni.showLoading({
					title: '保存中...'
				})

				// 构建更新数据
				const employeeId = this.employeeData.id

				if (!employeeId) {
					console.error('❌ 无法获取员工ID')
					console.log('📋 员工数据:', this.employeeData)
					throw new Error('无法获取员工ID')
				}

				console.log('✅ 员工ID:', employeeId)

				// 调试权限状态
				console.log('🔐 保存前权限状态检查:')
				console.log('🔐 permissions对象:', this.permissions)
				console.log('🔐 permissions键值:', Object.keys(this.permissions))
				console.log('🔐 originalPermissions对象:', this.originalPermissions)

				const updateData = {
					id: employeeId
				}

				// 添加变更的字段
				if (this.nicknameChanged) {
					updateData.nickname = this.formData.nickname
				}

				if (this.phoneChanged) {
					updateData.employeePhone = this.formData.employeePhone
				}

				if (this.avatarChanged) {
					updateData.avatar = this.currentAvatar
				}

				// 添加所有权限字段 - 总是发送完整的权限数据
				console.log('🔐 开始添加权限字段...')

				// 直接从员工数据获取当前权限值，如果permissions对象存在则使用它
				const permissionFields = ['canCount', 'canMeasure', 'canFeed', 'canPreSeed', 'canSample', 'canDocs', 'canAccount']

				permissionFields.forEach(field => {
					// 优先使用permissions对象中的值，如果不存在则使用原始数据中的值
					if (this.permissions && this.permissions[field]) {
						updateData[field] = this.permissions[field].value
						console.log(`🔐 从permissions对象获取 ${field}:`, this.permissions[field].value)
					} else if (this.employeeData && this.employeeData.hasOwnProperty(field)) {
						updateData[field] = this.employeeData[field]
						console.log(`🔐 从原始数据获取 ${field}:`, this.employeeData[field])
					} else {
						updateData[field] = false // 默认值
						console.log(`🔐 使用默认值 ${field}:`, false)
					}
				})

				console.log('🔐 最终权限数据:', {
					canCount: updateData.canCount,
					canMeasure: updateData.canMeasure,
					canFeed: updateData.canFeed,
					canPreSeed: updateData.canPreSeed,
					canSample: updateData.canSample,
					canDocs: updateData.canDocs,
					canAccount: updateData.canAccount
				})

				console.log('💾 开始保存员工信息')
				console.log('📤 完整更新数据:', updateData)
				console.log('📤 权限字段确认:', {
					canCount: updateData.canCount,
					canMeasure: updateData.canMeasure,
					canFeed: updateData.canFeed,
					canPreSeed: updateData.canPreSeed,
					canSample: updateData.canSample,
					canDocs: updateData.canDocs,
					canAccount: updateData.canAccount
				})
				console.log('📤 数据字段总数:', Object.keys(updateData).length)

				// 调用API更新员工信息
				const response = await this.safeNetworkRequest(
					userAPI.updateEmployee,
					updateData
				)

				console.log('📥 API响应:', response)

				if (response && response.code === 200) {
					console.log('✅ 员工信息更新成功')

					// 重置状态
					this.originalAvatar = this.currentAvatar
					this.originalNickname = this.formData.nickname
					this.originalPhone = this.formData.employeePhone
					this.originalPermissions = JSON.parse(JSON.stringify(this.permissions))
					this.avatarChanged = false
					this.nicknameChanged = false
					this.phoneChanged = false

					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})

					// 延迟返回上一页
					setTimeout(() => {
						this.goBack()
					}, 1500)

				} else {
					console.error('❌ API调用失败:', response)
					throw new Error((response && response.msg) || '保存失败')
				}

			} catch (error) {
				console.error('保存员工信息失败:', error)

				let errorMessage = error.message || '保存失败'
				if (error.message && error.message.includes('昵称')) {
					errorMessage = '昵称格式不正确'
				} else if (error.message && error.message.includes('电话')) {
					errorMessage = '电话格式不正确'
				} else if (error.message && error.message.includes('头像')) {
					errorMessage = '头像格式不正确'
				} else if (error.message && error.message.includes('权限')) {
					errorMessage = '权限设置失败'
				} else if (error.message && error.message.includes('网络')) {
					errorMessage = '网络异常，请重试'
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 2000
				})

			} finally {
				this.saving = false
				uni.hideLoading()
			}
		},

		goBack() {
			uni.navigateBack({
				delta: 1
			})
		}
	},

	onLoad(options) {
		console.log('🚀 ===== 员工编辑页面调试开始 =====')
		console.log('📱 页面加载时间:', new Date().toLocaleString())
		console.log('📋 原始页面参数:', options)
		console.log('📋 参数类型:', typeof options)
		console.log('📋 参数键值:', Object.keys(options))

		// 备份页面参数，防止在认证过程中丢失
		this.backupPageOptions = JSON.parse(JSON.stringify(options))
		console.log('💾 页面参数已备份:', this.backupPageOptions)

		// 立即解析员工数据，不等待认证完成
		this.parseEmployeeDataFromOptions(options)

		// 详细分析 employee 参数
		if (options.employee) {
			console.log('📦 原始 employee 参数:', options.employee)
			console.log('📦 employee 参数长度:', options.employee.length)
			console.log('📦 employee 参数类型:', typeof options.employee)

			try {
				// 尝试解码
				const decodedEmployee = decodeURIComponent(options.employee)
				console.log('🔓 解码后的 employee 数据:', decodedEmployee)
				console.log('🔓 解码数据长度:', decodedEmployee.length)

				// 尝试解析JSON
				const parsedEmployee = JSON.parse(decodedEmployee)
				console.log('📋 解析后的员工对象:', parsedEmployee)
				console.log('📋 员工对象类型:', typeof parsedEmployee)
				console.log('📋 员工对象键值:', Object.keys(parsedEmployee))

				// 详细分析每个字段
				console.log('🆔 员工ID字段分析:')
				console.log('  - id:', parsedEmployee.id, typeof parsedEmployee.id)
				console.log('  - user_id:', parsedEmployee.user_id, typeof parsedEmployee.user_id)
				console.log('  - employee_id:', parsedEmployee.employee_id, typeof parsedEmployee.employee_id)
				console.log('  - userId:', parsedEmployee.userId, typeof parsedEmployee.userId)
				console.log('  - employeeId:', parsedEmployee.employeeId, typeof parsedEmployee.employeeId)

				console.log('📱 员工基本信息:')
				console.log('  - nickname:', parsedEmployee.nickname, typeof parsedEmployee.nickname)
				console.log('  - employeePhone:', parsedEmployee.employeePhone, typeof parsedEmployee.employeePhone)
				console.log('  - avatar:', parsedEmployee.avatar ? `${parsedEmployee.avatar.substring(0, 50)}...` : parsedEmployee.avatar, typeof parsedEmployee.avatar)

				console.log('🔐 员工角色信息:')
				console.log('  - role:', parsedEmployee.role, typeof parsedEmployee.role)
				if (parsedEmployee.role) {
					console.log('  - role keys:', Object.keys(parsedEmployee.role))
					console.log('  - role.code:', parsedEmployee.role.code)
					console.log('  - role.name:', parsedEmployee.role.name)

					// 检查权限字段
					const permissionKeys = Object.keys(parsedEmployee.role).filter(key => key.startsWith('can'))
					console.log('  - 权限字段:', permissionKeys)
					permissionKeys.forEach(key => {
						console.log(`    - ${key}:`, parsedEmployee.role[key], typeof parsedEmployee.role[key])
					})
				}

				console.log('📊 完整员工数据结构:')
				console.log(JSON.stringify(parsedEmployee, null, 2))

			} catch (error) {
				console.error('❌ 解析员工数据时出错:', error)
				console.log('📦 原始数据:', options.employee)
			}
		} else {
			console.warn('⚠️ 缺少 employee 参数')
		}

		console.log('🚀 ===== 员工编辑页面调试结束 =====')
	},

	onAuthReady(authData) {
		console.log('� ===== 认证完成调试开始 =====')
		console.log('�👤 员工编辑页面收到认证完成事件:', authData)
		console.log('🔐 认证时间:', new Date().toLocaleString())
		console.log('🔐 认证状态:', {
			authReady: this.authReady,
			authSuccess: this.authSuccess,
			authError: this.authError
		})
		console.log('📋 当前页面选项:', this.pageOptions)
		console.log('� 备份页面选项:', this.backupPageOptions)
		console.log('📋 页面选项对比:', {
			current: this.pageOptions,
			backup: this.backupPageOptions,
			currentHasEmployee: !!(this.pageOptions && this.pageOptions.employee),
			backupHasEmployee: !!(this.backupPageOptions && this.backupPageOptions.employee)
		})
		console.log('�🔐 ===== 认证完成调试结束 =====')

		this.parsePageOptions()

		// 强制更新视图
		this.$nextTick(() => {
			console.log('🔄 强制更新视图完成')
			console.log('🔄 当前员工数据状态:', !!this.employeeData)
			if (this.employeeData) {
				console.log('🔄 员工数据内容:', this.employeeData)
			}
			this.$forceUpdate()
		})
	},

	onAuthFailed(errorData) {
		console.log('👤 员工编辑页面收到认证失败事件:', errorData)
	}
}
</script>

<style scoped>
/* 页面容器 */
.employee-edit-page {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 认证等待状态 */
.auth-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100vh;
	background-color: #f5f5f5;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #e9ecef;
	border-top: 4rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}

/* 认证错误状态 */
.auth-error {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100vh;
	padding: 40rpx;
	background-color: #f5f5f5;
}

.error-text {
	font-size: 32rpx;
	color: #ff4757;
	margin-bottom: 40rpx;
	text-align: center;
}

.retry-btn {
	background-color: #007aff;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

/* 页面内容 */
.page-content {
	padding: 20rpx;
	padding-bottom: 40rpx;
}

/* 调试信息区域 */
.debug-section {
	background-color: #fff3cd;
	border: 2rpx solid #ffeaa7;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.debug-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #856404;
	margin-bottom: 16rpx;
	display: block;
}

.debug-item {
	display: flex;
	margin-bottom: 12rpx;
	align-items: center;
}

.debug-item:last-child {
	margin-bottom: 0;
}

.debug-label {
	font-size: 24rpx;
	color: #856404;
	width: 160rpx;
	flex-shrink: 0;
}

.debug-value {
	font-size: 24rpx;
	color: #533f03;
	flex: 1;
	word-break: break-all;
}

/* 页面标题 */
.page-header {
	text-align: center;
	margin-bottom: 40rpx;
	padding: 30rpx 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	color: white;
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.page-title {
	font-size: 40rpx;
	font-weight: 700;
	margin-bottom: 12rpx;
	display: block;
}

.page-subtitle {
	font-size: 26rpx;
	opacity: 0.9;
}

/* 区域容器 */
.avatar-section,
.info-section,
.permissions-section,
.preview-section {
	background-color: white;
	border-radius: 20rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

/* 区域标题 */
.section-header {
	padding: 30rpx 30rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.section-desc {
	font-size: 24rpx;
	color: #999;
}

/* 头像设置区域 */
.avatar-container {
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.avatar-button {
	background: none;
	border: none;
	padding: 0;
	margin-bottom: 20rpx;
}

.avatar-button::after {
	border: none;
}

.avatar-wrapper {
	position: relative;
	width: 160rpx;
	height: 160rpx;
}

.avatar-preview {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background-color: #f0f0f0;
	border: 4rpx solid #fff;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.avatar-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: opacity 0.3s;
}

.avatar-button:active .avatar-overlay {
	opacity: 1;
}

.camera-icon {
	font-size: 40rpx;
	color: white;
	margin-bottom: 8rpx;
}

.change-text {
	font-size: 20rpx;
	color: white;
}

.change-indicator {
	display: flex;
	align-items: center;
	padding: 12rpx 20rpx;
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	border-radius: 20rpx;
	color: white;
}

.change-icon {
	font-size: 24rpx;
	margin-right: 8rpx;
}

/* 表单区域 */
.form-container {
	padding: 30rpx;
}

.form-group {
	margin-bottom: 32rpx;
	position: relative;
}

.form-group:last-child {
	margin-bottom: 0;
}

.form-label-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.form-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.char-count {
	font-size: 24rpx;
	color: #999;
}

.phone-format {
	font-size: 24rpx;
	color: #007aff;
}

.form-input {
	width: 100%;
	height: 88rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 12rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
	transition: all 0.3s;
}

.form-input:focus {
	border-color: #007aff;
	box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.field-changed {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	display: flex;
	align-items: center;
	padding: 4rpx 12rpx;
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
	border-radius: 12rpx;
	font-size: 20rpx;
	color: white;
}

/* 权限设置区域 */
.permissions-container {
	padding: 20rpx 30rpx 30rpx;
}

.permission-item {
	margin-bottom: 24rpx;
	border-radius: 16rpx;
	border: 2rpx solid #f0f0f0;
	overflow: hidden;
	transition: all 0.3s;
}

.permission-item:last-child {
	margin-bottom: 0;
}

.permission-item:hover {
	border-color: #007aff;
	box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.1);
}

.permission-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
}

.permission-main {
	display: flex;
	align-items: center;
	flex: 1;
}

.permission-icon {
	font-size: 40rpx;
	margin-right: 20rpx;
}

.permission-info {
	flex: 1;
}

.permission-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.permission-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.permission-control {
	margin-left: 20rpx;
}

.permission-changed {
	padding: 8rpx 24rpx;
	background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
	display: flex;
	align-items: center;
	font-size: 22rpx;
	color: #333;
}

/* 预览区域 */
.preview-container {
	padding: 30rpx;
}

.preview-card {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
	border-radius: 16rpx;
}

.preview-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
	border: 2rpx solid white;
}

.preview-info {
	flex: 1;
}

.preview-nickname {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.preview-phone {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}

.preview-changes {
	font-size: 22rpx;
	color: #007aff;
	font-weight: 500;
}

/* 操作按钮区域 */
.action-section {
	display: flex;
	gap: 20rpx;
	padding: 0 20rpx;
	margin-top: 40rpx;
}

.save-btn {
	flex: 2;
	height: 88rpx;
	border: none;
	border-radius: 16rpx;
	font-size: 28rpx;
	font-weight: 600;
	transition: all 0.3s;
}

.save-btn-active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
}

.save-btn-disabled {
	background-color: #e9ecef;
	color: #999;
}

.cancel-btn {
	flex: 1;
	height: 88rpx;
	background-color: #f8f9fa;
	color: #666;
	border: 2rpx solid #e9ecef;
	border-radius: 16rpx;
	font-size: 28rpx;
	transition: all 0.3s;
}

.cancel-btn:active {
	background-color: #e9ecef;
	transform: scale(0.98);
}
</style>