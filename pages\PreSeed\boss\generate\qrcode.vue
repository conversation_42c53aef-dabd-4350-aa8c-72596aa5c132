<template>
	<view class="container">
		<!-- 认证等待状态 -->
		<view v-if="!showPageContent" class="auth-waiting">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ pageStatusText }}</text>
			</view>
		</view>

		<!-- 页面主要内容 - 只有认证成功后才显示 -->
		<view v-if="showPageContent" class="main-content">
			<view class="header">
				<text class="title">试水苗二维码</text>
				<text class="subtitle">生成试水苗二维码</text>
			</view>

			<view class="content">
				<!-- 苗场信息 -->
				<view class="miaochang-info" v-if="qrCodeData && qrCodeData.miaochang">
					<text class="miaochang-name">{{ (qrCodeData.miaochang && qrCodeData.miaochang.name) || '未设置' }}</text>
					<text class="miaochang-desc">试水苗二维码</text>
				</view>

				<!-- 二维码显示区域 -->
				<view class="qrcode-section" v-if="qrCodeData && qrCodeData.wechatQrCode">
					<view class="qrcode-container">
						<image
							class="qrcode-image"
							:src="qrCodeData.wechatQrCode"
							mode="aspectFit"
							@error="onQrImageError"
							@load="onQrImageLoad"
						/>
						<text class="qrcode-tip">长按保存二维码</text>
					</view>
				</view>

				<!-- 加载状态 -->
				<view class="loading-section" v-if="loading">
					<view class="loading-spinner"></view>
					<text class="loading-text">正在生成二维码...</text>
				</view>

				<!-- 错误信息 -->
				<view class="error-section" v-if="errorMessage">
					<text class="error-icon">⚠️</text>
					<text class="error-text">{{ errorMessage }}</text>
					<button class="retry-btn" @click="generateQrCode">重新生成</button>
				</view>

				<!-- 操作按钮 -->
				<view class="action-buttons" v-if="qrCodeData">
		
					<button class="btn refresh-btn" @click="generateQrCode">重新生成</button>
				</view>

				<button class="btn back-btn" @click="goBack">返回</button>
			</view>
		</view>

		<!-- 认证失败状态 -->
		<view v-if="authReady && !authSuccess" class="auth-failed">
			<view class="error-container">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{ authError || '登录验证失败' }}</text>
				<text class="error-desc">无法生成二维码，请重新登录</text>
				<button class="retry-btn" @click="goBack">返回首页</button>
			</view>
		</view>
	</view>
</template>

<script>
import { wechatAPI } from '../../../../api/auth'
import { mapActions } from 'vuex'
import pageEventMixin from '@/mixins/pageEvent.js'

export default {
	name: 'PreseedBossQrCodePage',
	mixins: [pageEventMixin],

	data() {
		return {
			qrCodeData: null,    // 二维码数据
			loading: false,      // 是否正在加载
			errorMessage: '',    // 错误信息
			generating: false    // 是否正在生成中
		}
	},

	onLoad(options) {
		console.log('预种子二维码页面加载，参数:', options)
		// pageEventMixin 会自动保存参数并设置事件监听
	},

	methods: {
		...mapActions('auth', ['saveLocalAuth']),
		...mapActions('user', ['setUserInfo']),

		/**
		 * 生成预种子二维码
		 */
		async generateQrCode() {
			if (!this.canMakeNetworkRequest) {
				console.warn('🚫 认证未完成，无法生成二维码')
				this.errorMessage = '认证未完成，无法生成二维码'
				return
			}

			// 防止重复请求
			if (this.generating) {
				console.warn('🚫 正在生成中，请勿重复请求')
				return
			}

			// 清除之前的状态
			this.errorMessage = ''
			this.qrCodeData = null

			try {
				this.generating = true
				this.loading = true

				console.log('👤 开始生成预种子二维码')

				// 调用API生成二维码
				const response = await this.safeNetworkRequest(wechatAPI.getPreseedBossQrCode)

				console.log('📥 生成二维码API响应:', response)

				if (response.code === 200) {
					this.qrCodeData = response.results
					console.log('✅ 二维码生成成功:', this.qrCodeData)
					console.log('🏢 苗场名称:', this.qrCodeData.miaochang && this.qrCodeData.miaochang.name)
					console.log('🔗 二维码URL:', this.qrCodeData.wechatQrCode)

					uni.showToast({
						title: '二维码生成成功',
						icon: 'success'
					})
				} else {
					// 显示错误信息
					this.errorMessage = response.msg || '生成二维码失败'
					uni.showToast({
						title: response.msg || '生成失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('生成二维码失败:', error)
				this.errorMessage = '网络错误，请重试'
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				this.generating = false
				this.loading = false
			}
		},

		/**
		 * 二维码图片加载成功
		 */
		onQrImageLoad() {
			console.log('✅ 二维码图片加载成功')
		},

		/**
		 * 二维码图片加载错误处理
		 */
		onQrImageError(e) {
			console.error('二维码图片加载失败:', e)
			this.errorMessage = '二维码图片加载失败'
			uni.showToast({
				title: '图片加载失败',
				icon: 'none'
			})
		},

		/**
		 * 保存二维码到相册
		 */
		async saveQrCode() {
			if (!this.qrCodeData || !this.qrCodeData.wechatQrCode) {
				uni.showToast({
					title: '没有可保存的二维码',
					icon: 'none'
				})
				return
			}

			try {
				uni.showLoading({
					title: '保存中...'
				})

				// 下载图片到本地
				const downloadResult = await uni.downloadFile({
					url: this.qrCodeData.wechatQrCode
				})

				if (downloadResult.statusCode === 200) {
					// 保存到相册
					await uni.saveImageToPhotosAlbum({
						filePath: downloadResult.tempFilePath
					})

					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
				} else {
					throw new Error('下载失败')
				}
			} catch (error) {
				console.error('保存二维码失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 分享二维码说明
		 */
		shareQrCode() {
			if (!this.qrCodeData || !this.qrCodeData.wechatQrCode) {
				uni.showToast({
					title: '没有可分享的二维码',
					icon: 'none'
				})
				return
			}

			// 在微信小程序中，需要用户主动触发分享
			// 这里提示用户使用右上角分享按钮
			uni.showModal({
				title: '分享提示',
				content: '请点击右上角的"..."按钮，选择"转发"来分享二维码页面给好友',
				showCancel: false,
				confirmText: '知道了'
			})
		},

		/**
		 * 认证完成回调 - 由 App.vue 事件触发
		 * 只有认证成功后才会执行
		 */
		onAuthReady(authData) {
			console.log('👤 预种子二维码页面收到认证完成事件:', authData)
			console.log('✅ 页面现在可以安全地生成二维码')

			// 认证完成后自动生成二维码
			this.generateQrCode()
		},

		/**
		 * 认证失败回调 - 由 App.vue 事件触发
		 */
		onAuthFailed(errorData) {
			console.log('👤 预种子二维码页面收到认证失败事件:', errorData)
			console.log('❌ 页面无法生成二维码，显示错误状态')

			// 认证失败时的错误信息已经由 mixin 处理
			// 页面内容已经被隐藏，显示错误状态
		},

		goBack() {
			uni.navigateBack({
				delta: 1
			})
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 认证等待状态样式 */
.auth-waiting {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.loading-container {
	text-align: center;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #e9ecef;
	border-top: 4rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin: 0 auto 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}

/* 主要内容样式 */
.main-content {
	min-height: 100vh;
	padding: 40rpx;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	display: block;
	font-size: 48rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.subtitle {
	display: block;
	font-size: 28rpx;
	color: #666;
}

.content {
	max-width: 600rpx;
	margin: 0 auto;
}
/* 苗场信息样式 */
.miaochang-info {
	text-align: center;
	margin-bottom: 60rpx;
	padding: 40rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.miaochang-name {
	display: block;
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 10rpx;
}

.miaochang-desc {
	display: block;
	font-size: 24rpx;
	color: #666;
}

/* 二维码区域样式 */
.qrcode-section {
	margin-bottom: 60rpx;
}

.qrcode-container {
	text-align: center;
	padding: 40rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.qrcode-image {
	width: 400rpx;
	height: 400rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
}

.qrcode-tip {
	display: block;
	font-size: 24rpx;
	color: #999;
}

/* 加载状态样式 */
.loading-section {
	text-align: center;
	padding: 80rpx 40rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	margin-bottom: 60rpx;
}

/* 错误状态样式 */
.error-section {
	text-align: center;
	padding: 60rpx 40rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	margin-bottom: 60rpx;
}

.error-icon {
	display: block;
	font-size: 80rpx;
	margin-bottom: 20rpx;
}

.error-text {
	display: block;
	font-size: 28rpx;
	color: #dc3545;
	margin-bottom: 30rpx;
}

.retry-btn {
	width: 200rpx;
	height: 60rpx;
	line-height: 60rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 30rpx;
	font-size: 24rpx;
	border: none;
}

/* 操作按钮样式 */
.action-buttons {
	margin-bottom: 40rpx;
}

.btn {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
	margin-bottom: 20rpx;
	text-align: center;
}

.primary-btn {
	background-color: #28a745;
	color: #fff;
}

.secondary-btn {
	background-color: #17a2b8;
	color: #fff;
}

.refresh-btn {
	background-color: #ffc107;
	color: #333;
}

.back-btn {
	background-color: #6c757d;
	color: #fff;
}

/* 认证失败状态样式 */
.auth-failed {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.error-container {
	text-align: center;
	padding: 60rpx 40rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	margin: 40rpx;
}

.error-desc {
	display: block;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 30rpx;
}
</style>
