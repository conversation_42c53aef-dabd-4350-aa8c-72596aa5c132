# 功能模块总结

## 1. 用户认证系统

### 1.1 静默登录机制
**实现位置**: `store/modules/auth.js`

#### 核心流程
```javascript
// 静默登录主流程
async silentLogin() {
  // 1. 检查本地token
  const localAuth = await dispatch('loadLocalAuth')
  
  // 2. 检查60分钟缓存
  const timeDiff = now - (localAuth.lastVerifyTime || 0)
  const CACHE_DURATION = 1 * 60 * 1000 // 60分钟
  
  if (timeDiff < CACHE_DURATION) {
    // 使用缓存token
    return { success: true, fromCache: true }
  }
  
  // 3. 验证token有效性
  return await dispatch('verifyToken', localAuth.token)
}
```

#### 特色功能
- **60分钟缓存策略**: 避免频繁网络验证
- **自动降级机制**: token失效时自动执行完整登录
- **防重复请求**: 登录状态锁定机制

### 1.2 JWT Token管理
**实现位置**: `store/modules/auth.js`

#### Token生命周期
1. **获取**: 微信登录获取初始token
2. **验证**: 定期验证token有效性
3. **刷新**: 自动刷新过期token
4. **清除**: 登出或异常时清除

#### 存储策略
```javascript
// 本地存储结构
{
  token: "JWT_TOKEN",
  refreshToken: "REFRESH_TOKEN", 
  lastVerifyTime: 1642147200000,
  userInfo: { /* 用户信息 */ }
}
```

### 1.3 事件驱动认证
**实现位置**: `App.vue`

#### 认证事件系统
```javascript
// 认证完成事件
uni.$emit('auth-ready', {
  success: true,
  fromCache: result.fromCache,
  userInfo: this.$store.state.user.userInfo
})

// 认证失败事件
uni.$emit('auth-failed', {
  success: false,
  error: error.message
})
```

## 2. 员工管理系统

### 2.1 员工列表管理
**实现位置**: `pages/employee/list/index.vue`

#### 核心功能
- **员工列表展示**: 分页加载员工数据
- **权限控制**: 基于角色的功能访问控制
- **操作入口**: 编辑、登录码生成

#### API接口
```javascript
// 员工列表API
GET /api/user/employee/list/
// 返回格式: {count, next, previous, results}
```

### 2.2 员工信息编辑
**实现位置**: `pages/employee/edit/index.vue`

#### 编辑功能
- **基本信息**: 头像、昵称、电话
- **权限设置**: canCount、canMeasure、canFeed等
- **角色管理**: 员工角色分配

#### 权限字段
```javascript
{
  canCount: boolean,    // 计数权限
  canMeasure: boolean,  // 测量权限  
  canFeed: boolean,     // 喂养权限
  canPreSeed: boolean,  // 预播种权限
  canSample: boolean,   // 采样权限
  canDocs: boolean,     // 文档权限
  canAccount: boolean   // 账户权限
}
```

### 2.3 员工登录码
**实现位置**: `pages/employee/logincode/index.vue`

#### 登录码功能
- **二维码生成**: 为员工生成专属登录二维码
- **安全机制**: 基于员工ID的唯一标识
- **使用场景**: 员工快速登录系统

## 3. 代理商管理系统

### 3.1 代理商列表
**实现位置**: `pages/Agent/list/agentlist.vue`

#### 列表功能
- **分页展示**: 支持下拉刷新和上拉加载
- **状态管理**: 启用/禁用代理商
- **信息编辑**: 昵称、电话、备注修改

#### API接口
```javascript
// 代理商列表
GET /api/miaochang/agent/list/
// 代理商更新  
POST /api/miaochang/agent/update/
// 代理商状态控制
GET /api/miaochang/agent/disable/{id}
```

### 3.2 代理商激活
**实现位置**: `pages/Agent/ActAgent/index.vue`

#### 激活流程
1. **扫码获取**: 通过二维码获取激活码
2. **信息验证**: 验证代理码有效性
3. **确认激活**: 提交激活确认

## 4. 预播种系统

### 4.1 二维码生成
**实现位置**: `pages/PreSeed/boss/generate/qrcode.vue`

#### 生成功能
- **权限控制**: 仅角色8888或5555可访问
- **二维码生成**: 调用API生成预播种二维码
- **苗场信息**: 显示关联的苗场名称

#### API接口
```javascript
// 预播种二维码生成
GET /api/wechat/preseed/boss/qrcode/get/
// 返回: {code, msg, results: {wechatQrCode, miaochang}}
```

### 4.2 二维码验证
**实现位置**: `pages/PreSeed/verify/qrcode.vue`

#### 验证流程
1. **扫码解析**: 解析二维码中的no参数
2. **API验证**: 调用验证接口检查有效性
3. **信息展示**: 显示苗场名称和数量
4. **确认操作**: 用户确认后完成验证

### 4.3 操作记录
**实现位置**: `pages/PreSeed/logs/index.vue`

#### 记录功能
- **历史查看**: 查看预播种操作历史
- **详细信息**: 苗场、操作人、时间、数量
- **分页加载**: 支持大量数据的分页展示

## 5. 苗场激活系统

### 5.1 苗场激活流程
**实现位置**: `pages/MiaoChang/ActMiaoChang/index.vue`

#### 激活步骤
1. **扫码获取**: 通过二维码scene参数获取激活码
2. **信息查询**: 调用API获取激活码详情
3. **确认激活**: 用户确认后提交激活请求
4. **状态更新**: 激活成功后更新用户状态

#### 关键API
```javascript
// 获取激活码信息
GET /api/wechat/miaochang/activate/get/?code={code}
// 确认激活
POST /api/wechat/miaochang/activate/confirm/
```

## 6. 个人中心系统

### 6.1 用户信息展示
**实现位置**: `pages/profile/index.vue`

#### 展示内容
- **基本信息**: 头像、昵称、角色名称
- **角色菜单**: 基于role.code的差异化菜单
- **功能入口**: 各种管理功能的入口

#### 角色菜单配置
```javascript
// 角色8888: 超级管理员
menuItems: ['中介管理', '登录管理']

// 角色3333: 试水苗管理员  
menuItems: ['试水苗管理']

// 其他角色: 无额外菜单
menuItems: []
```

### 6.2 头像昵称更新
**实现位置**: `pages/avatar/index.vue`

#### 更新功能
- **微信原生**: 使用微信小程序原生头像昵称更新
- **时间限制**: 基于last_avatar_time的更新限制
- **格式处理**: base64格式图片上传

#### 时间验证逻辑
```javascript
// 时间验证规则
if (currentTime > last_avatar_time) {
  // 允许更新
} else {
  // 拒绝更新，显示剩余时间
}
```

## 7. 权限控制系统

### 7.1 角色权限矩阵
```javascript
const ROLE_PERMISSIONS = {
  8888: {
    name: "超级管理员",
    permissions: ["中介管理", "登录管理", "预播种管理"]
  },
  5555: {
    name: "高级管理员", 
    permissions: ["预播种管理"]
  },
  3333: {
    name: "试水苗管理员",
    permissions: ["试水苗管理"]
  }
}
```

### 7.2 页面级权限控制
- **认证检查**: 所有页面等待auth-ready事件
- **角色验证**: 基于用户角色控制功能访问
- **动态菜单**: 根据权限动态显示菜单项

---

*本文档详细描述了项目的各个功能模块实现，为开发和维护提供参考*
