<template>
	<view class="container">
		<!-- 认证等待状态 -->
		<view v-if="!showPageContent" class="auth-waiting">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ pageStatusText }}</text>
			</view>
		</view>

		<!-- 页面主要内容 - 只有认证成功后才显示 -->
		<view v-if="showPageContent" class="main-content">
			<view class="header">
				<text class="title">成为代理</text>
				<text class="subtitle">扫码成为代理</text>
			</view>

			<view class="content">
				<!-- 代理码输入区域 -->
				<view class="input-section" v-if="!showConfirm">
					<view class="qr-info" v-if="agentCode">
						<text class="info-text">检测到代理邀请码: {{ agentCode }}</text>
					</view>

					<view class="manual-input" v-if="!agentCode">
						<text class="label">代理码</text>
						<input
							class="input"
							v-model="agentCode"
							placeholder="请输入代理码"
							maxlength="20"
						/>
					</view>

					<button
						class="check-btn"
						@click="checkAgentCode(agentCode)"
						:disabled="!agentCode.trim() || !canMakeNetworkRequest || checking"
					>
						{{ checking ? '检查中...' : (canMakeNetworkRequest ? '检查代理码' : '认证中...') }}
					</button>
				</view>

				<!-- 代理信息确认区域 -->
				<view class="confirm-section" v-if="showConfirm && agentInfo">
					<view class="agent-info">
						<text class="info-title">代理信息</text>
						<view class="info-item">
							<text class="info-label">苗场名称:</text>
							<text class="info-value">{{ (agentInfo.miaochang && agentInfo.miaochang.name) || '未设置' }}</text>
						</view>
						<view class="info-item" v-if="agentInfo.agentCode">
							<text class="info-label">代理码:</text>
							<text class="info-value">{{ agentInfo.agentCode }}</text>
						</view>
						<view class="info-item" v-if="agentInfo.remark_admin">
							<text class="info-label">管理员备注:</text>
							<text class="info-value">{{ agentInfo.remark_admin }}</text>
						</view>
						<view class="info-item" v-if="agentInfo.expiredTime">
							<text class="info-label">过期时间:</text>
							<text class="info-value">{{ agentInfo.expiredTime }}</text>
						</view>
						<view class="qr-section" v-if="agentInfo.miniQrCode">
							<text class="info-label">代理二维码:</text>
							<image
								class="qr-image"
								:src="agentInfo.miniQrCode"
								mode="aspectFit"
								@error="onQrImageError"
							/>
						</view>
					</view>

					<button
						class="confirm-btn"
						@click="confirmActivation"
						:disabled="activating"
					>
						{{ activating ? '激活中...' : '确认激活' }}
					</button>

					<button
						class="cancel-btn"
						@click="showConfirm = false"
						:disabled="activating"
					>
						取消
					</button>
				</view>

				<!-- 结果显示区域 -->
				<view class="result" v-if="result">
					<text class="result-text">{{ result }}</text>
				</view>

				<button class="btn" @click="goBack" v-if="!showConfirm">返回首页</button>
			</view>
		</view>

		<!-- 认证失败状态 -->
		<view v-if="authReady && !authSuccess" class="auth-failed">
			<view class="error-container">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{ authError || '登录验证失败' }}</text>
				<text class="error-desc">无法处理代理激活，请重新登录</text>
				<button class="retry-btn" @click="goBack">返回首页</button>
			</view>
		</view>
	</view>
</template>

<script>
import { wechatAPI } from '../../../api/auth'
import { mapActions } from 'vuex'
import pageEventMixin from '@/mixins/pageEvent.js'

export default {
	name: 'AgentActivePage',
	mixins: [pageEventMixin],

	data() {
		return {
			agentCode: '',
			result: '',
			agentInfo: null,     // 代理信息
			showConfirm: false,  // 是否显示确认按钮
			activating: false,   // 是否正在激活中
			checking: false      // 是否正在检查代理码
		}
	},

	onLoad(options) {
		console.log('代理激活页面加载，参数:', options)

		// 解析微信小程序码的scene参数
		this.parseSceneParams(options)

		// pageEventMixin 会自动保存参数并设置事件监听
	},

	methods: {
		...mapActions('auth', ['saveLocalAuth']),
		...mapActions('user', ['setUserInfo']),

		/**
		 * 解析微信小程序码的scene参数
		 * 支持格式: scene="code=ABC123" 或直接 code="ABC123"
		 */
		parseSceneParams(options) {
			console.log('🔍 开始解析页面参数:', options)

			let extractedCode = null

			// 方式1: 直接从code参数获取 (普通链接跳转)
			if (options.code) {
				extractedCode = options.code
				console.log('✅ 从code参数获取代理码:', extractedCode)
			}
			// 方式2: 从scene参数解析 (微信小程序码扫码)
			else if (options.scene) {
				console.log('🔍 检测到scene参数:', options.scene)

				try {
					// 解码scene参数 (可能被URL编码)
					const decodedScene = decodeURIComponent(options.scene)
					console.log('🔓 解码后的scene:', decodedScene)

					// 解析 "code=ABC123" 格式
					const codeMatch = decodedScene.match(/code=([^&]+)/)
					if (codeMatch && codeMatch[1]) {
						extractedCode = codeMatch[1]
						console.log('✅ 从scene参数解析出代理码:', extractedCode)
					} else {
						console.warn('⚠️ scene参数格式不正确:', decodedScene)
					}
				} catch (error) {
					console.error('❌ 解析scene参数失败:', error)
				}
			}

			// 保存解析出的代理码
			if (extractedCode) {
				this.agentCode = extractedCode
				console.log('📋 设置代理码:', this.agentCode)
			} else {
				console.warn('⚠️ 未找到有效的代理码参数')
			}
		},

		/**
		 * 检查代理码有效性
		 */
		async checkAgentCode(code) {
			if (!this.canMakeNetworkRequest) {
				console.warn('🚫 认证未完成，无法检查代理码')
				this.result = '认证未完成，无法检查代理码'
				return
			}

			// 防止重复请求
			if (this.checking) {
				console.warn('🚫 正在检查中，请勿重复请求')
				return
			}

			// 清除之前的状态
			this.result = ''
			this.agentInfo = null
			this.showConfirm = false

			try {
				this.checking = true
				uni.showLoading({
					title: '检查中...'
				})

				console.log('👤 开始检查代理码有效性')
				console.log('📋 代理码:', code)

				// 使用安全的网络请求方法检查代理码
				const response = await this.safeNetworkRequest(wechatAPI.checkAgentCode, code)

				console.log('📥 检查API响应:', response)

				if (response.code === 200) {
					// 显示代理信息，等待用户确认
					this.agentInfo = response.results
					this.showConfirm = true

					console.log('✅ 代理码有效，显示信息:', this.agentInfo)
					console.log('🏢 苗场名称:', this.agentInfo.miaochang && this.agentInfo.miaochang.name)
					console.log('🔑 代理码:', this.agentInfo.agentCode)

					const miaochangName = this.agentInfo.miaochang && this.agentInfo.miaochang.name
					uni.showToast({
						title: `验证成功：${miaochangName || '代理码有效'}`,
						icon: 'success'
					})
				} else {
					// 显示错误信息
					this.result = response.msg || '代理码无效'
					uni.showToast({
						title: response.msg || '验证失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('检查代理码失败:', error)
				this.result = '网络错误，请重试'
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				this.checking = false
				uni.hideLoading()
			}
		},

		/**
		 * 确认激活代理
		 */
		async confirmActivation() {
			if (this.activating) return

			if (!this.agentInfo) {
				uni.showToast({
					title: '代理信息不存在',
					icon: 'none'
				})
				return
			}

			try {
				this.activating = true
				uni.showLoading({
					title: '激活中...'
				})

				console.log('👤 开始确认激活代理')
				console.log('📋 发送代理信息:', this.agentInfo)

				// 使用新的确认激活API
				const response = await this.safeNetworkRequest(wechatAPI.confirmAgentActivation, this.agentInfo)

				console.log('📥 确认激活API响应:', response)

				if (response.code === 200) {
					this.result = '恭喜您成为代理！'
					this.showConfirm = false

					uni.showToast({
						title: '激活成功',
						icon: 'success'
					})

					// 激活成功后返回首页
					setTimeout(() => {
						this.goBack()
					}, 1500)
				} else {
					// 显示服务器返回的错误信息
					this.result = response.msg || '激活失败'
					uni.showToast({
						title: response.msg || '激活失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('确认激活代理失败:', error)
				this.result = error.message || '激活失败'
				uni.showToast({
					title: error.message || '激活失败',
					icon: 'none'
				})
			} finally {
				this.activating = false
				uni.hideLoading()
			}
		},
		// 安全的处理代理激活 - 只有在认证成功后才能执行
		async handleAgentActivation(code) {
			if (!this.canMakeNetworkRequest) {
				console.warn('🚫 认证未完成，无法处理代理激活')
				this.result = '认证未完成，无法处理代理激活'
				return
			}

			try {
				uni.showLoading({
					title: '激活中...'
				})

				console.log('👤 开始安全的代理激活请求')

				// 使用安全的网络请求方法
				const response = await this.safeNetworkRequest(wechatAPI.verifyInviteAgent, code)
				
				if (response.code === 200) {
					this.result = '恭喜您成为代理！'

					// 更新用户信息 - 适配新的返回格式
					const { jwtToken, user } = response.results
					if (jwtToken && user && user.role) {
						const { token, refresh } = jwtToken
						// 确保user对象包含role属性
						await this.setUserInfo(user)
						await this.saveLocalAuth({
							token,
							refreshToken: refresh,
							userInfo: user
						})
					}
					
					uni.showToast({
						title: '激活成功',
						icon: 'success'
					})
				} else {
					this.result = response.msg || '激活失败'
					uni.showToast({
						title: '激活失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('代理激活失败:', error)
				this.result = '网络错误，请重试'
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 二维码图片加载错误处理
		 */
		onQrImageError(e) {
			console.error('二维码图片加载失败:', e)
			uni.showToast({
				title: '二维码加载失败',
				icon: 'none'
			})
		},

		// 返回首页
		goBack() {
			uni.reLaunch({
				url: '/pages/index/index'
			})
		},

		/**
		 * 认证完成回调 - 由 App.vue 事件触发
		 * 只有认证成功后才会执行
		 */
		onAuthReady(authData) {
			console.log('👤 代理页面收到认证完成事件:', authData)
			console.log('✅ 代理页面现在可以安全地处理激活逻辑')

			// 认证完成后安全地处理页面参数
			let codeToCheck = null

			if (this.pageOptions && this.pageOptions.code) {
				codeToCheck = this.pageOptions.code
				this.agentCode = codeToCheck
				console.log('🔍 从页面参数获取代理邀请码:', codeToCheck)
			} else if (this.agentCode) {
				codeToCheck = this.agentCode
				console.log('🔍 使用已有的代理码:', codeToCheck)
			}

			if (codeToCheck) {
				console.log('🔍 开始检查代理码有效性:', codeToCheck)
				this.checkAgentCode(codeToCheck)
			} else {
				console.log('📝 没有代理邀请码参数')
			}
		},

		/**
		 * 认证失败回调 - 由 App.vue 事件触发
		 */
		onAuthFailed(errorData) {
			console.log('👤 代理页面收到认证失败事件:', errorData)
			console.log('❌ 代理页面无法处理激活逻辑，显示错误状态')

			// 认证失败时的错误信息已经由 mixin 处理
			// 页面内容已经被隐藏，显示错误状态
		}
	}
}
</script>

<style>
.container {
	padding: 40rpx;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 48rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
}

.content {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.qr-info {
	margin-bottom: 40rpx;
	padding: 30rpx;
	background-color: #f0f0f0;
	border-radius: 10rpx;
}

.info-text {
	font-size: 28rpx;
	color: #333;
	word-break: break-all;
}

.result {
	margin-bottom: 40rpx;
	padding: 30rpx;
	background-color: #e8f5e8;
	border-radius: 10rpx;
	text-align: center;
}

.result-text {
	font-size: 32rpx;
	color: #4cd964;
}

.btn {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 44rpx;
	font-size: 32rpx;
	border: none;
}

/* 认证等待和失败状态样式 */
.auth-waiting, .auth-failed {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.loading-container, .error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
	background-color: #fff;
	border-radius: 20rpx;
	margin: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid #e0e0e0;
	border-top: 6rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text, .error-text {
	font-size: 28rpx;
	text-align: center;
	margin-bottom: 20rpx;
}

.loading-text {
	color: #666;
}

.error-text {
	color: #dd524d;
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}

.error-desc {
	font-size: 24rpx;
	color: #999;
	text-align: center;
	margin-bottom: 40rpx;
}

.retry-btn {
	width: 200rpx;
	height: 70rpx;
	line-height: 70rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: none;
}

.main-content {
	min-height: 100vh;
}

/* 输入区域样式 */
.input-section {
	margin-bottom: 40rpx;
}

.manual-input {
	margin-bottom: 30rpx;
}

.label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
	font-weight: 500;
}

.input {
	width: 100%;
	height: 80rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	font-size: 28rpx;
	background-color: #fff;
	box-sizing: border-box;
	margin-bottom: 20rpx;
}

.check-btn {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	background-color: #28a745;
	color: #fff;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
	margin-bottom: 20rpx;
}

.check-btn:disabled {
	background-color: #6c757d;
}

/* 确认区域样式 */
.confirm-section {
	margin-bottom: 40rpx;
}

.agent-info {
	background-color: #f8f9fa;
	padding: 30rpx;
	border-radius: 12rpx;
	margin-bottom: 30rpx;
}

.info-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.info-item {
	display: flex;
	margin-bottom: 15rpx;
	align-items: flex-start;
}

.info-label {
	font-size: 28rpx;
	color: #666;
	min-width: 120rpx;
	flex-shrink: 0;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
	word-break: break-all;
}

.confirm-btn {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
	margin-bottom: 20rpx;
}

.confirm-btn:disabled {
	background-color: #6c757d;
}

.cancel-btn {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	background-color: #6c757d;
	color: #fff;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
}

/* 二维码样式 */
.qr-section {
	margin-top: 20rpx;
	text-align: center;
}

.qr-image {
	width: 200rpx;
	height: 200rpx;
	margin-top: 10rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
}
</style>
