<template>
	<view class="container">
		<!-- 认证等待状态 -->
		<view v-if="!showPageContent" class="auth-waiting">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ pageStatusText }}</text>
			</view>
		</view>

		<!-- 页面主要内容 - 只有认证成功后才显示 -->
		<view v-if="showPageContent" class="main-content">
			<!-- 头像编辑区域 -->
			<view class="avatar-section">
				<text class="section-title">头像设置</text>
				<view class="avatar-edit-container">
					<!-- 使用微信内置头像选择 -->
					<button
						class="avatar-button"
						open-type="chooseAvatar"
						@chooseavatar="onChooseAvatar"
					>
						<view class="avatar-wrapper">
							<image
								class="avatar-preview"
								:src="currentAvatar || defaultAvatar"
								mode="aspectFill"
								@error="onAvatarError"
							></image>
							<view class="avatar-overlay">
								<text class="camera-icon">📷</text>
								<text class="change-text">点击更换</text>
							</view>
						</view>
					</button>
					<text class="avatar-tip">点击头像使用微信头像</text>

					<!-- 备用头像选择方案 -->
					<view class="backup-options">
						<button class="backup-btn" @click="chooseImageFromAlbum">
							<text class="backup-icon">🖼️</text>
							<text class="backup-text">从相册选择</text>
						</button>
					</view>
				</view>
			</view>

			<!-- 昵称编辑区域 -->
			<view class="nickname-section">
				<text class="section-title">昵称设置</text>
				<view class="nickname-edit-container">
					<!-- 使用微信内置昵称输入 -->
					<input
						class="nickname-input"
						type="nickname"
						v-model="currentNickname"
						placeholder="请输入昵称"
						maxlength="20"
						:disabled="saving"
						@blur="onNicknameBlur"
					/>
					<text class="nickname-counter">{{ currentNickname.length }}/20</text>
				</view>
				<text class="nickname-tip">使用微信昵称或自定义昵称</text>
			</view>



			<!-- 操作按钮区域 -->
			<view class="action-section">
				<button
					:class="saveButtonClass"
					:disabled="saving || !hasChanges"
					@click="saveChanges"
				>
					<text v-if="saving">保存中...</text>
					<text v-else>保存修改</text>
				</button>

				<button class="cancel-btn" @click="goBack" :disabled="saving">
					取消
				</button>
			</view>

			<!-- 预览信息 -->
			<view class="preview-section" v-if="hasChanges">
				<text class="preview-title">预览</text>
				<view class="preview-card">
					<image 
						class="preview-avatar" 
						:src="currentAvatar || defaultAvatar" 
						mode="aspectFill"
					></image>
					<view class="preview-info">
						<text class="preview-nickname">{{ currentNickname || '未设置昵称' }}</text>
						<text class="preview-role">{{ userRoleName }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 认证失败状态 -->
		<view v-if="authReady && !authSuccess" class="auth-failed">
			<view class="error-container">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{ authError || '登录验证失败' }}</text>
				<text class="error-desc">无法编辑用户信息，请重新登录</text>
				<button class="retry-btn" @click="retryAuth">重试</button>
			</view>
		</view>

		<!-- 隐藏的canvas用于图片压缩 -->
		<canvas canvas-id="imageCanvas" style="width: 400px; height: 400px; position: fixed; top: -1000px; left: -1000px;"></canvas>
	</view>
</template>

<script>
import { mapGetters, mapActions, mapState } from 'vuex'
import pageEventMixin from '@/mixins/pageEvent.js'
import { userAPI, adaptUserUpdateResponse, validateUserUpdateData } from '@/api/user.js'
import { checkUserAvatarTime, formatRemainingTime } from '@/utils/avatarTimeValidator.js'

export default {
	name: 'AvatarPage',
	mixins: [pageEventMixin],

	data() {
		return {
			defaultAvatar: '/static/default-avatar.png',
			currentAvatar: '',
			currentNickname: '',
			originalAvatar: '',
			originalNickname: '',
			saving: false,
			avatarChanged: false,
			nicknameChanged: false
		}
	},

	computed: {
		...mapGetters('auth', ['isTokenValid']),
		...mapState('user', ['userInfo']),

		// 是否有修改
		hasChanges() {
			return this.avatarChanged || this.nicknameChanged
		},

		// 用户角色名称
		userRoleName() {
			if (this.userInfo && this.userInfo.role && this.userInfo.role.name) {
				return this.userInfo.role.name
			}
			return '普通用户'
		},

		// 保存按钮样式类
		saveButtonClass() {
			let classes = ['save-btn']
			if (this.saving) {
				classes.push('saving')
			}
			if (!this.hasChanges) {
				classes.push('disabled')
			}
			return classes.join(' ')
		}
	},

	watch: {
		// 监听昵称变化
		currentNickname(newVal, oldVal) {
			console.log('👀 昵称变化监听触发:')
			console.log('  - oldVal:', oldVal)
			console.log('  - newVal:', newVal)
			console.log('  - originalNickname:', this.originalNickname)

			this.nicknameChanged = newVal !== this.originalNickname

			console.log('  - nicknameChanged:', this.nicknameChanged)
			console.log('  - hasChanges:', this.hasChanges)
		}
	},

	methods: {
		...mapActions('auth', ['silentLogin']),
		...mapActions('user', ['setUserInfo']),

		onLoad() {
			console.log('头像编辑页面加载')
			// pageEventMixin 会自动设置事件监听
		},

		/**
		 * 认证完成回调 - 由 App.vue 事件触发
		 */
		onAuthReady(authData) {
			console.log('🖼️ 头像编辑页面收到认证完成事件:', authData)
			console.log('✅ 头像编辑页面现在可以安全地编辑用户信息')
			
			// 初始化用户数据
			this.initUserData()
		},

		/**
		 * 认证失败回调 - 由 App.vue 事件触发
		 */
		onAuthFailed(errorData) {
			console.log('🖼️ 头像编辑页面收到认证失败事件:', errorData)
			console.log('❌ 头像编辑页面无法编辑用户信息，显示错误状态')
		},

		/**
		 * 初始化用户数据
		 */
		initUserData() {
			console.log('📊 初始化用户数据:', this.userInfo)
			
			this.originalAvatar = this.userInfo.avatar || ''
			this.originalNickname = this.userInfo.nickname || ''
			
			this.currentAvatar = this.originalAvatar
			this.currentNickname = this.originalNickname
			
			this.avatarChanged = false
			this.nicknameChanged = false
		},

		/**
		 * 微信内置头像选择回调
		 */
		async onChooseAvatar(e) {
			console.log('🖼️ 微信内置头像选择回调:', e)

			if (!this.canExecutePageLogic) {
				console.warn('⚠️ 认证未完成，无法选择头像')
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			if (this.saving) {
				console.warn('⚠️ 正在保存中，无法选择头像')
				return
			}

			try {
				const { avatarUrl } = e.detail || {}
				console.log('📋 头像选择事件详情:', e.detail)

				if (!avatarUrl) {
					throw new Error('未获取到头像信息，请重试')
				}

				// 检查URL格式
				if (typeof avatarUrl !== 'string' || avatarUrl.trim() === '') {
					throw new Error('头像URL格式无效')
				}

				uni.showLoading({
					title: '处理头像中...'
				})

				console.log('📷 获取到微信头像URL:', avatarUrl)
				console.log('🔄 开始转换头像为base64格式')

				// 将微信头像转换为base64
				const base64 = await this.convertWechatAvatarToBase64(avatarUrl)

				if (!base64 || !base64.startsWith('data:image/')) {
					throw new Error('头像转换失败，格式无效')
				}

				console.log('✅ 头像转换完成，base64长度:', base64.length)

				// 更新状态
				this.currentAvatar = base64
				this.avatarChanged = true

				console.log('✅ 头像状态更新完成:')
				console.log('  - avatarChanged:', this.avatarChanged)
				console.log('  - hasChanges:', this.hasChanges)

				uni.showToast({
					title: '头像选择成功',
					icon: 'success'
				})

			} catch (error) {
				console.error('❌ 获取微信头像失败:', error)

				// 根据错误类型提供不同的提示
				let errorMessage = '获取头像失败'

				if (error.message.includes('文件不存在') || error.message.includes('ENOENT')) {
					errorMessage = '头像文件访问失败，请重试'
				} else if (error.message.includes('网络')) {
					errorMessage = '网络异常，请检查网络连接'
				} else if (error.message.includes('超时')) {
					errorMessage = '操作超时，请重试'
				} else if (error.message.includes('格式')) {
					errorMessage = '头像格式不支持'
				} else if (error.message) {
					errorMessage = error.message
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})

				// 在开发环境下显示详细错误信息
				if (process.env.NODE_ENV === 'development') {
					console.error('🔍 详细错误信息:', {
						message: error.message,
						stack: error.stack,
						detail: e.detail
					})
				}
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 将微信头像URL转换为base64
		 */
		async convertWechatAvatarToBase64(avatarUrl) {
			return new Promise((resolve, reject) => {
				console.log('🔄 开始转换微信头像:', avatarUrl)

				// 检查是否是有效的URL
				if (!avatarUrl || typeof avatarUrl !== 'string') {
					console.error('❌ 无效的头像URL:', avatarUrl)
					reject(new Error('无效的头像URL'))
					return
				}

				// 如果已经是base64格式，直接返回
				if (avatarUrl.startsWith('data:image/')) {
					console.log('✅ 头像已经是base64格式')
					resolve(avatarUrl)
					return
				}

				// 下载微信头像
				uni.downloadFile({
					url: avatarUrl,
					timeout: 10000, // 10秒超时
					success: (downloadRes) => {
						console.log('📥 下载响应:', downloadRes)

						if (downloadRes.statusCode === 200 && downloadRes.tempFilePath) {
							console.log('✅ 下载成功，临时文件路径:', downloadRes.tempFilePath)

							// 检查文件是否存在
							const fileManager = uni.getFileSystemManager()

							try {
								// 先检查文件状态
								fileManager.access({
									path: downloadRes.tempFilePath,
									success: () => {
										console.log('✅ 文件存在，开始读取')
										// 读取文件并转换为base64
										fileManager.readFile({
											filePath: downloadRes.tempFilePath,
											encoding: 'base64',
											success: (readRes) => {
												try {
													// 添加数据URL前缀
													const base64 = `data:image/jpeg;base64,${readRes.data}`
													console.log('✅ 微信头像转base64成功，长度:', base64.length)
													resolve(base64)
												} catch (error) {
													console.error('❌ base64处理失败:', error)
													reject(new Error('头像格式处理失败'))
												}
											},
											fail: (error) => {
												console.error('❌ 读取头像文件失败:', error)
												reject(new Error(`文件读取失败: ${error.errMsg || '未知错误'}`))
											}
										})
									},
									fail: (error) => {
										console.error('❌ 文件不存在或无法访问:', error)
										reject(new Error(`文件访问失败: ${error.errMsg || '文件不存在'}`))
									}
								})
							} catch (error) {
								console.error('❌ 文件系统操作异常:', error)
								reject(new Error('文件系统操作失败'))
							}
						} else {
							console.error('❌ 下载失败，状态码:', downloadRes.statusCode)
							reject(new Error(`下载失败，状态码: ${downloadRes.statusCode}`))
						}
					},
					fail: (error) => {
						console.error('❌ 下载微信头像失败:', error)
						reject(new Error(`下载失败: ${error.errMsg || '网络错误'}`))
					}
				})
			})
		},

		/**
		 * 昵称输入失焦回调
		 */
		onNicknameBlur(e) {
			console.log('📝 昵称输入失焦:', e.detail.value)
			console.log('📋 昵称变化状态:')
			console.log('  - originalNickname:', this.originalNickname)
			console.log('  - currentNickname:', this.currentNickname)
			console.log('  - nicknameChanged:', this.nicknameChanged)
			console.log('  - hasChanges:', this.hasChanges)
			// 微信会自动处理昵称的合规性检查
		},

		/**
		 * 备用方案：从相册选择头像
		 */
		async chooseImageFromAlbum() {
			if (!this.canExecutePageLogic) {
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			if (this.saving) {
				return
			}

			try {
				console.log('🖼️ 使用备用方案：从相册选择头像')

				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album'],
					success: async (res) => {
						try {
							uni.showLoading({
								title: '处理头像中...'
							})

							const tempFilePath = res.tempFilePaths[0]
							console.log('📷 选择的图片路径:', tempFilePath)

							// 转换为base64
							const base64 = await this.convertImageToBase64(tempFilePath)

							if (!base64 || !base64.startsWith('data:image/')) {
								throw new Error('图片转换失败')
							}

							// 更新状态
							this.currentAvatar = base64
							this.avatarChanged = true

							console.log('✅ 相册头像设置成功')
							uni.showToast({
								title: '头像选择成功',
								icon: 'success'
							})

						} catch (error) {
							console.error('❌ 处理相册图片失败:', error)
							uni.showToast({
								title: '图片处理失败',
								icon: 'none'
							})
						} finally {
							uni.hideLoading()
						}
					},
					fail: (error) => {
						console.error('❌ 选择图片失败:', error)
						if (error.errMsg && !error.errMsg.includes('cancel')) {
							uni.showToast({
								title: '选择图片失败',
								icon: 'none'
							})
						}
					}
				})

			} catch (error) {
				console.error('❌ 相册选择失败:', error)
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				})
			}
		},

		/**
		 * 将本地图片转换为base64
		 */
		async convertImageToBase64(filePath) {
			return new Promise((resolve, reject) => {
				const fileManager = uni.getFileSystemManager()

				fileManager.readFile({
					filePath: filePath,
					encoding: 'base64',
					success: (res) => {
						const base64 = `data:image/jpeg;base64,${res.data}`
						resolve(base64)
					},
					fail: (error) => {
						console.error('读取图片文件失败:', error)
						reject(new Error('图片读取失败'))
					}
				})
			})
		},





		/**
		 * 保存修改
		 */
		async saveChanges() {
			if (!this.canMakeNetworkRequest) {
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			if (this.saving || !this.hasChanges) {
				return
			}

			try {
				// 前置时间验证
				console.log('🕐 开始前置时间验证...')
				const timeCheck = checkUserAvatarTime(this.userInfo)
				console.log('⏰ 时间验证结果:', timeCheck)

				if (!timeCheck.canUpdate) {
					const errorMessage = formatRemainingTime(timeCheck.remainingMinutes || 0)
					console.error('❌ 前置时间验证失败:', errorMessage)

					uni.showModal({
						title: '更新限制',
						content: `更新过于频繁，${errorMessage}`,
						showCancel: false,
						confirmText: '我知道了'
					})
					return
				}

				console.log('✅ 前置时间验证通过')

				// 验证数据
				const updateData = {}

				console.log('🔍 检查数据变化状态:')
				console.log('  - nicknameChanged:', this.nicknameChanged)
				console.log('  - avatarChanged:', this.avatarChanged)
				console.log('  - currentNickname:', this.currentNickname)
				console.log('  - currentAvatar length:', this.currentAvatar ? this.currentAvatar.length : 0)

				if (this.nicknameChanged) {
					if (!this.currentNickname.trim()) {
						throw new Error('昵称不能为空')
					}
					updateData.nickname = this.currentNickname.trim()
					console.log('✅ 添加昵称到更新数据:', updateData.nickname)
				}

				if (this.avatarChanged) {
					updateData.avatar = this.currentAvatar
					console.log('✅ 添加头像到更新数据，长度:', this.currentAvatar ? this.currentAvatar.length : 0)
				}

				console.log('📋 最终更新数据:', {
					...updateData,
					avatar: updateData.avatar ? `${updateData.avatar.substring(0, 50)}...` : undefined
				})

				// 传递当前用户信息进行验证（包括时间验证）
				console.log('🔍 当前用户信息:', this.userInfo)
				validateUserUpdateData(updateData, this.userInfo)
				console.log('✅ 数据验证通过（包括时间验证）')

				this.saving = true
				uni.showLoading({
					title: '保存中...'
				})

				console.log('💾 开始调用API更新用户信息')
				console.log('🔗 API方法:', 'userAPI.batchUpdateProfile')
				console.log('📤 请求数据:', {
					...updateData,
					avatar: updateData.avatar ? `${updateData.avatar.substring(0, 50)}...` : undefined
				})

				// 调用API更新用户信息
				const response = await this.safeNetworkRequest(
					userAPI.batchUpdateProfile,
					updateData
				)

				console.log('📥 API响应:', response)

				// 适配响应数据
				const result = adaptUserUpdateResponse(response)
				console.log('🔧 适配后结果:', result)

				if (result.success) {
					console.log('✅ API调用成功，开始更新本地用户信息')
					console.log('👤 新用户信息:', result.user)

					// 更新本地用户信息
					await this.setUserInfo(result.user)
					console.log('✅ 本地用户信息更新完成')

					// 重置状态
					this.originalAvatar = this.currentAvatar
					this.originalNickname = this.currentNickname
					this.avatarChanged = false
					this.nicknameChanged = false
					console.log('✅ 页面状态重置完成')

					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})

					// 延迟返回上一页
					setTimeout(() => {
						this.goBack()
					}, 1500)

				} else {
					console.error('❌ API调用失败:', result.error)
					throw new Error(result.error || '保存失败')
				}

			} catch (error) {
				console.error('保存用户信息失败:', error)

				// 特殊处理时间验证错误
				let errorMessage = error.message || '保存失败'
				let toastDuration = 2000

				if (error.message && error.message.includes('更新过于频繁')) {
					console.log('⏰ 检测到时间验证错误，显示特殊提示')
					toastDuration = 3000 // 时间限制错误显示更长时间

					// 可以在这里添加更详细的时间信息显示
					uni.showModal({
						title: '更新限制',
						content: error.message,
						showCancel: false,
						confirmText: '我知道了'
					})
					return // 不显示toast，已经显示了modal
				} else if (error.message && error.message.includes('昵称')) {
					errorMessage = '昵称格式不正确'
				} else if (error.message && error.message.includes('头像')) {
					errorMessage = '头像格式不正确'
				} else if (error.message && error.message.includes('网络')) {
					errorMessage = '网络异常，请重试'
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: toastDuration
				})
			} finally {
				this.saving = false
				uni.hideLoading()
			}
		},

		/**
		 * 返回上一页
		 */
		goBack() {
			if (this.hasChanges && !this.saving) {
				uni.showModal({
					title: '确认离开',
					content: '您有未保存的修改，确定要离开吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack()
						}
					}
				})
			} else {
				uni.navigateBack()
			}
		},

		/**
		 * 头像加载失败处理
		 */
		onAvatarError() {
			console.log('头像加载失败，使用默认头像')
		},

		/**
		 * 重试认证
		 */
		retryAuth() {
			console.log('🔄 用户手动重试认证')
			this.authReady = false
			this.authSuccess = false
			this.authError = null
			this.showPageContent = false
			this.pageLoading = true
			this.waitForAuth()
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 认证等待状态样式 */
.auth-waiting {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
	background-color: #fff;
	border-radius: 20rpx;
	margin: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid #e0e0e0;
	border-top: 6rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

/* 主要内容样式 */
.main-content {
	min-height: 100vh;
	padding: 40rpx 30rpx;
}

/* 区域标题 */
.section-title {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #fff;
	margin-bottom: 30rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 头像编辑区域 */
.avatar-section {
	margin-bottom: 60rpx;
}

.avatar-edit-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.avatar-button {
	background: none;
	border: none;
	padding: 0;
	margin: 0;
	margin-bottom: 20rpx;
}

.avatar-button::after {
	border: none;
}

.avatar-wrapper {
	position: relative;
	cursor: pointer;
}

.avatar-preview {
	width: 200rpx;
	height: 200rpx;
	border-radius: 100rpx;
	border: 6rpx solid #f0f0f0;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.avatar-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	border-radius: 100rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.avatar-wrapper:active .avatar-overlay {
	opacity: 1;
}

.camera-icon {
	font-size: 48rpx;
	color: #fff;
	margin-bottom: 8rpx;
}

.change-text {
	font-size: 24rpx;
	color: #fff;
}

.avatar-tip {
	font-size: 24rpx;
	color: #999;
	text-align: center;
	margin-bottom: 20rpx;
}

/* 备用头像选择选项 */
.backup-options {
	display: flex;
	justify-content: center;
	margin-top: 10rpx;
}

.backup-btn {
	display: flex;
	align-items: center;
	padding: 15rpx 25rpx;
	background-color: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 25rpx;
	font-size: 24rpx;
	color: #666;
	transition: all 0.2s ease;
}

.backup-btn::after {
	border: none;
}

.backup-btn:active {
	background-color: #e9ecef;
	transform: scale(0.98);
}

.backup-icon {
	font-size: 28rpx;
	margin-right: 8rpx;
}

.backup-text {
	font-size: 24rpx;
}

/* 昵称编辑区域 */
.nickname-section {
	margin-bottom: 60rpx;
}

.nickname-edit-container {
	position: relative;
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.nickname-input {
	width: 100%;
	height: 80rpx;
	font-size: 32rpx;
	color: #333;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 20rpx;
	box-sizing: border-box;
}

.nickname-input:focus {
	border-color: #667eea;
}

.nickname-input[disabled] {
	background-color: #f5f5f5;
	color: #999;
}

.nickname-counter {
	position: absolute;
	right: 30rpx;
	bottom: 15rpx;
	font-size: 24rpx;
	color: #999;
}

.nickname-tip {
	display: block;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-top: 15rpx;
	text-align: center;
}



/* 操作按钮区域 */
.action-section {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 40rpx;
}

.save-btn, .cancel-btn {
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 600;
	border: none;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.save-btn {
	background-color: #52c41a;
	color: #fff;
}

.save-btn.disabled {
	background-color: #d9d9d9;
	color: #999;
}

.save-btn.saving {
	background-color: #1890ff;
}

.cancel-btn {
	background-color: #fff;
	color: #666;
	border: 2rpx solid #d9d9d9;
}

/* 预览区域 */
.preview-section {
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.preview-title {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.preview-card {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.preview-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	margin-right: 20rpx;
}

.preview-info {
	flex: 1;
}

.preview-nickname {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.preview-role {
	font-size: 24rpx;
	color: #666;
}

/* 认证失败状态样式 */
.auth-failed {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
	background-color: #fff;
	border-radius: 20rpx;
	margin: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}

.error-text {
	font-size: 28rpx;
	color: #dd524d;
	text-align: center;
	margin-bottom: 20rpx;
}

.error-desc {
	font-size: 24rpx;
	color: #999;
	text-align: center;
	margin-bottom: 40rpx;
}

.retry-btn {
	width: 200rpx;
	height: 70rpx;
	line-height: 70rpx;
	background-color: #667eea;
	color: #fff;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: none;
}
</style>
