<script>
import { mapActions, mapGetters } from 'vuex'

export default {
	computed: {
		...mapGetters('auth', ['isTokenValid', 'loginLoading'])
	},

	methods: {
		...mapActions('auth', ['silentLogin', 'clearAuth']),

		/**
		 * 获取当前认证状态
		 */
		getAuthStatus() {
			return {
				isReady: this.$store.state.auth.hasLaunched,
				isLoggedIn: this.isTokenValid,
				userInfo: this.$store.state.user.userInfo
			}
		},

		/**
		 * 通知页面认证完成
		 */
		notifyAuthReady(result) {
			console.log('📢 发送认证完成事件:', result)
			uni.$emit('auth-ready', {
				success: true,
				fromCache: result.fromCache,
				userInfo: this.$store.state.user.userInfo
			})
		},

		/**
		 * 通知页面认证失败
		 */
		notifyAuthFailed(error) {
			console.log('📢 发送认证失败事件:', error)
			uni.$emit('auth-failed', {
				success: false,
				error: error.message || '认证失败'
			})
		}
	},

	async onLaunch() {
		console.log('🚀 App Launch - 开始静默登录')

		// 安全地将认证状态存储到全局变量
		try {
			const app = getApp()
			if (app) {
				if (!app.globalData) {
					app.globalData = {}
				}
				app.globalData.authStatusGetter = this.getAuthStatus.bind(this)
				console.log('✅ 认证状态查询方法已存储到全局变量')
			}
		} catch (error) {
			console.warn('存储认证状态查询方法失败:', error)
			// 如果 getApp() 失败，我们稍后在页面中直接使用 store
		}

		try {
			// 显示加载提示
			uni.showLoading({
				title: '登录中...',
				mask: true
			})

			// 执行静默登录
			console.log('📱 调用静默登录方法')
			const result = await this.silentLogin()
			console.log('📱 静默登录结果:', result)

			if (result.success) {
				const loginType = result.fromCache ? '使用缓存' : '验证通过'
				console.log('✅ 应用级静默登录成功:', loginType)

				// 通知所有页面认证完成
				this.notifyAuthReady(result)

				uni.showToast({
					title: '登录成功',
					icon: 'success',
					duration: 1500
				})
			} else {
				console.log('❌ 应用级静默登录失败:', result.message || '未知原因')

				// 通知所有页面认证失败
				this.notifyAuthFailed(new Error(result.message || '登录失败'))

				uni.showToast({
					title: result.message || '登录失败，请重试',
					icon: 'none',
					duration: 2000
				})
			}

		} catch (error) {
			console.error('💥 应用级静默登录异常:', error)
			// 清除可能损坏的认证信息
			await this.clearAuth()

			// 通知所有页面认证异常
			this.notifyAuthFailed(error)

			uni.showToast({
				title: '登录异常，请重试',
				icon: 'none',
				duration: 2000
			})
		} finally {
			uni.hideLoading()
		}
	},

	async onShow() {
		console.log('App Show')

		// 如果应用已启动但登录状态异常，重新进行认证
		if (this.$store.state.auth.hasLaunched && !this.isTokenValid) {
			console.log('检测到登录状态异常，重新进行认证')
			try {
				const result = await this.silentLogin()
				if (result.success) {
					this.notifyAuthReady(result)
				} else {
					this.notifyAuthFailed(new Error(result.message || '重新登录失败'))
				}
			} catch (error) {
				console.error('重新登录失败:', error)
				this.notifyAuthFailed(error)
			}
		}
	},

	onHide() {
		console.log('App Hide')
	}
}
</script>

<style>
	/*每个页面公共css */
</style>
