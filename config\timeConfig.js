/**
 * 时间验证配置
 * 用于控制用户操作的时间限制
 */

export const TIME_CONFIG = {
  /**
   * 头像更新冷却时间（分钟）
   * 0 = 无限制
   * 60 = 1小时
   * 1440 = 24小时
   */
  AVATAR_COOLDOWN_MINUTES: 0,
  
  /**
   * 昵称更新冷却时间（分钟）
   * 0 = 无限制
   * 30 = 30分钟
   * 60 = 1小时
   */
  NICKNAME_COOLDOWN_MINUTES: 0,
  
  /**
   * 用户信息批量更新冷却时间（分钟）
   * 当同时更新头像和昵称时使用
   */
  PROFILE_BATCH_COOLDOWN_MINUTES: 0,
  
  /**
   * 是否启用时间验证
   * true = 启用时间限制
   * false = 禁用时间限制（开发模式）
   */
  ENABLE_TIME_VALIDATION: true,
  
  /**
   * 时间验证的宽松模式
   * true = 验证失败时只警告，不阻止操作
   * false = 验证失败时阻止操作
   */
  LENIENT_MODE: false,
  
  /**
   * 错误提示配置
   */
  ERROR_MESSAGES: {
    AVATAR_COOLDOWN: '头像更新过于频繁',
    NICKNAME_COOLDOWN: '昵称更新过于频繁',
    PROFILE_COOLDOWN: '用户信息更新过于频繁',
    TIME_PARSE_ERROR: '时间格式错误',
    VALIDATION_ERROR: '时间验证失败'
  },
  
  /**
   * 时间格式配置
   */
  TIME_FORMAT: {
    DISPLAY: 'YYYY-MM-DD HH:mm:ss',
    API: 'ISO',
    LOG: 'YYYY-MM-DD HH:mm:ss.SSS'
  }
}

/**
 * 根据操作类型获取冷却时间
 * @param {string} operationType 操作类型：'avatar', 'nickname', 'batch'
 * @returns {number} 冷却时间（分钟）
 */
export function getCooldownMinutes(operationType) {
  if (!TIME_CONFIG.ENABLE_TIME_VALIDATION) {
    return 0 // 禁用时间验证时返回0
  }
  
  switch (operationType) {
    case 'avatar':
      return TIME_CONFIG.AVATAR_COOLDOWN_MINUTES
    case 'nickname':
      return TIME_CONFIG.NICKNAME_COOLDOWN_MINUTES
    case 'batch':
    case 'profile':
      return TIME_CONFIG.PROFILE_BATCH_COOLDOWN_MINUTES
    default:
      console.warn('未知的操作类型:', operationType)
      return 0
  }
}

/**
 * 获取错误提示信息
 * @param {string} errorType 错误类型
 * @param {number} remainingMinutes 剩余分钟数
 * @returns {string} 错误提示信息
 */
export function getErrorMessage(errorType, remainingMinutes = 0) {
  const baseMessage = TIME_CONFIG.ERROR_MESSAGES[errorType] || '操作过于频繁'
  
  if (remainingMinutes <= 0) {
    return baseMessage
  }
  
  if (remainingMinutes < 60) {
    return `${baseMessage}，还需等待 ${remainingMinutes} 分钟`
  }
  
  const hours = Math.floor(remainingMinutes / 60)
  const minutes = remainingMinutes % 60
  
  if (minutes === 0) {
    return `${baseMessage}，还需等待 ${hours} 小时`
  }
  
  return `${baseMessage}，还需等待 ${hours} 小时 ${minutes} 分钟`
}

/**
 * 检查是否为开发环境
 * @returns {boolean} 是否为开发环境
 */
export function isDevelopment() {
  // 可以根据实际情况调整判断逻辑
  return process.env.NODE_ENV === 'development' || 
         uni.getSystemInfoSync().platform === 'devtools'
}

/**
 * 开发环境配置
 * 在开发环境下可以使用更宽松的时间限制
 */
export const DEV_TIME_CONFIG = {
  ...TIME_CONFIG,
  AVATAR_COOLDOWN_MINUTES: 0,
  NICKNAME_COOLDOWN_MINUTES: 0,
  PROFILE_BATCH_COOLDOWN_MINUTES: 0,
  LENIENT_MODE: true
}

/**
 * 获取当前环境的时间配置
 * @returns {object} 时间配置对象
 */
export function getCurrentTimeConfig() {
  return isDevelopment() ? DEV_TIME_CONFIG : TIME_CONFIG
}

/**
 * 时间验证预设配置
 */
export const PRESET_CONFIGS = {
  // 无限制模式（开发/测试）
  NO_LIMIT: {
    AVATAR_COOLDOWN_MINUTES: 0,
    NICKNAME_COOLDOWN_MINUTES: 0,
    PROFILE_BATCH_COOLDOWN_MINUTES: 0,
    ENABLE_TIME_VALIDATION: false
  },
  
  // 宽松模式
  LENIENT: {
    AVATAR_COOLDOWN_MINUTES: 5,
    NICKNAME_COOLDOWN_MINUTES: 2,
    PROFILE_BATCH_COOLDOWN_MINUTES: 5,
    ENABLE_TIME_VALIDATION: true,
    LENIENT_MODE: true
  },
  
  // 标准模式
  STANDARD: {
    AVATAR_COOLDOWN_MINUTES: 30,
    NICKNAME_COOLDOWN_MINUTES: 10,
    PROFILE_BATCH_COOLDOWN_MINUTES: 30,
    ENABLE_TIME_VALIDATION: true,
    LENIENT_MODE: false
  },
  
  // 严格模式
  STRICT: {
    AVATAR_COOLDOWN_MINUTES: 1440, // 24小时
    NICKNAME_COOLDOWN_MINUTES: 60,  // 1小时
    PROFILE_BATCH_COOLDOWN_MINUTES: 1440, // 24小时
    ENABLE_TIME_VALIDATION: true,
    LENIENT_MODE: false
  }
}

export default {
  TIME_CONFIG,
  getCooldownMinutes,
  getErrorMessage,
  isDevelopment,
  getCurrentTimeConfig,
  PRESET_CONFIGS
}
