<template>
	<view class="container">
		<!-- 认证等待状态 -->
		<view v-if="!showPageContent" class="auth-waiting">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ pageStatusText }}</text>
			</view>
		</view>

		<!-- 页面主要内容 - 只有认证成功后才显示 -->
		<view v-if="showPageContent" class="main-content">
			<view class="header">
				<text class="title">生成中介邀请码</text>
				<text class="subtitle">为您的账户生成专属邀请码</text>
			</view>

			<view class="form-section">
				<!-- 用户信息显示区域 -->
				

			
				<!-- 提交按钮 -->
				<view class="action-section">
					<button
						:class="submitButtonClass"
						:disabled="!canSubmit"
						@click="generateInviteCode"
					>
						{{ submitting ? '生成中...' : (inviteResult ? '重新获取邀请码' : '生成邀请码') }}
					</button>
				</view>

				<!-- 结果显示区域 -->
				<view v-if="inviteResult" class="result-section">
					<view class="result-card">
					
						<view class="result-content">
							
							<view class="result-item qr-code-item" v-if="inviteResult.miniQrCode">

								<view class="qr-code-container">
									<!-- 喵厂名称显示 -->
									<view v-if="inviteResult.miaochang && inviteResult.miaochang.name" class="miaochang-name">
										<!-- <text class="miaochang-label">喵厂名称:</text> -->
										<text class="miaochang-value">{{ inviteResult.miaochang.name }}</text>
									</view>

									<image
										class="qr-code-image"
										:src="inviteResult.miniQrCode"
										mode="aspectFit"
										@error="onQrCodeError"
										@load="onQrCodeLoad"
									/>

									<text class="qr-code-tip">长按识别小程序码</text>
								</view>
							</view>
							<view class="result-item" v-if="inviteResult.created_at">
								<text class="result-label">生成时间:</text>
								<text class="result-value">{{ formatTime(inviteResult.created_at) }}</text>
							</view>
						</view>
						<view class="result-actions">
							<button v-if="inviteResult.miniQrCode" class="save-btn" @click="saveQrCode">保存小程序码</button>
						</view>
					</view>
				</view>

		
				
			</view>
		</view>

		<!-- 认证失败状态 -->
		<view v-if="authReady && !authSuccess" class="auth-failed">
			<view class="error-container">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{ authError || '登录验证失败' }}</text>
				<text class="error-desc">无法生成中介邀请码，请重新登录</text>
				<button class="retry-btn" @click="goBack">返回首页</button>
			</view>
		</view>
	</view>
</template>

<script>
import { wechatAPI } from '../../../api/auth'
import { mapActions, mapGetters } from 'vuex'
import pageEventMixin from '@/mixins/pageEvent.js'

export default {
	name: 'InviteAgentPage',
	mixins: [pageEventMixin],

	data() {
		return {
			submitting: false,
			inviteResult: null
		}
	},

	computed: {
		...mapGetters('user', ['userInfo']),

		// 提交按钮样式类
		submitButtonClass() {
			let classes = ['submit-btn']
			if (this.submitting) {
				classes.push('submitting')
			}
			if (!this.canSubmit) {
				classes.push('disabled')
			}
			return classes.join(' ')
		},

		// 是否可以提交
		canSubmit() {
			return !this.submitting && this.canMakeNetworkRequest
		}
	},

	onLoad(options) {
		console.log('中介邀请码生成页面加载，参数:', options)
		// pageEventMixin 会自动保存参数并设置事件监听
	},

	methods: {
		...mapActions('auth', ['saveLocalAuth']),
		...mapActions('user', ['setUserInfo']),

		

		/**
		 * 生成邀请码
		 */
		async generateInviteCode() {
			if (!this.canMakeNetworkRequest) {
				console.warn('🚫 认证未完成，无法生成邀请码')
				uni.showToast({
					title: '请等待认证完成',
					icon: 'none'
				})
				return
			}

			try {
				this.submitting = true
				uni.showLoading({
					title: this.inviteResult ? '获取中...' : '生成中...'
				})

				console.log('📱 开始获取/生成中介邀请码')
				console.log('👤 当前用户信息:', this.userInfo)

				// 调用API获取或生成邀请码
				const response = await this.safeNetworkRequest(wechatAPI.getInviteAgent)

				console.log('📥 邀请码API响应:', response)

				if (response.code === 200) {
					this.inviteResult = response.results

					console.log('✅ 邀请码获取成功:', this.inviteResult)
					console.log('🔍 二维码链接:', this.inviteResult.miniQrCode)
					console.log('🏢 喵厂信息:', this.inviteResult.miaochang)

					const successMessage = this.inviteResult.agent_code ?
						(this.inviteResult.created_at ? '邀请码获取成功' : '邀请码生成成功') :
						'邀请码生成成功'

					uni.showToast({
						title: successMessage,
						icon: 'success'
					})
				} else {
					throw new Error(response.msg || '获取邀请码失败')
				}
			} catch (error) {
				console.error('获取邀请码失败:', error)

				let errorMessage = error.message || '操作失败，请重试'

				if (error.message && error.message.includes('网络')) {
					errorMessage = '网络异常，请重试'
				} else if (error.message && error.message.includes('权限')) {
					errorMessage = '权限不足，请联系管理员'
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				this.submitting = false
				uni.hideLoading()
			}
		},



		/**
		 * 二维码加载成功
		 */
		onQrCodeLoad() {
			console.log('✅ 二维码图片加载成功')
		},

		/**
		 * 二维码加载失败
		 */
		onQrCodeError(e) {
			console.error('❌ 二维码图片加载失败:', e)
			uni.showToast({
				title: '二维码加载失败',
				icon: 'none'
			})
		},

		/**
		 * 保存二维码到相册
		 */
		async saveQrCode() {
			if (!this.inviteResult || !this.inviteResult.miniQrCode) {
				return
			}

			try {
				uni.showLoading({
					title: '保存中...'
				})

				// 下载图片到本地
				const downloadRes = await new Promise((resolve, reject) => {
					uni.downloadFile({
						url: this.inviteResult.miniQrCode,
						success: resolve,
						fail: reject
					})
				})

				if (downloadRes.statusCode === 200) {
					// 保存到相册
					await new Promise((resolve, reject) => {
						uni.saveImageToPhotosAlbum({
							filePath: downloadRes.tempFilePath,
							success: resolve,
							fail: reject
						})
					})

					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
				} else {
					throw new Error('下载失败')
				}
			} catch (error) {
				console.error('保存二维码失败:', error)

				let errorMessage = '保存失败'
				if (error.errMsg && error.errMsg.includes('auth')) {
					errorMessage = '请授权访问相册'
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		/**
		 * 格式化时间
		 */
		formatTime(timeString) {
			if (!timeString) return '--'
			
			try {
				const date = new Date(timeString)
				return date.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit'
				})
			} catch (error) {
				console.error('时间格式化失败:', error)
				return timeString
			}
		},

		/**
		 * 返回首页
		 */
		goBack() {
			uni.reLaunch({
				url: '/pages/index/index'
			})
		},

		/**
		 * 认证完成回调 - 由 App.vue 事件触发
		 * 只有认证成功后才会执行
		 */
		onAuthReady(authData) {
			console.log('📱 中介邀请码页面收到认证完成事件:', authData)
			console.log('✅ 中介邀请码页面现在可以安全地处理业务逻辑')

			// 认证完成后可以进行其他初始化操作
		},

		/**
		 * 认证失败回调 - 由 App.vue 事件触发
		 */
		onAuthFailed(errorData) {
			console.log('📱 中介邀请码页面收到认证失败事件:', errorData)
			console.log('❌ 中介邀请码页面无法处理业务逻辑，显示错误状态')

			// 认证失败时的错误信息已经由 mixin 处理
			// 页面内容已经被隐藏，显示错误状态
		}
	}
}
</script>

<style>
.container {
	padding: 40rpx;
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 认证等待状态样式 */
.auth-waiting {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid rgba(255, 255, 255, 0.3);
	border-top: 6rpx solid #fff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	color: rgba(255, 255, 255, 0.9);
	font-size: 28rpx;
	text-align: center;
}

/* 页面主要内容样式 */
.main-content {
	width: 100%;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 20rpx;
}

.subtitle {
	display: block;
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}

/* 表单区域样式 */
.form-section {
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.input-section {
	margin-bottom: 40rpx;
}

.input-label {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.input-container {
	position: relative;
	margin-bottom: 10rpx;
}

.phone-input {
	width: 100%;
	height: 80rpx;
	font-size: 32rpx;
	color: #333;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 80rpx 0 20rpx;
	box-sizing: border-box;
	background-color: #fff;
}

.phone-input:focus {
	border-color: #667eea;
}

.phone-input[disabled] {
	background-color: #f5f5f5;
	color: #999;
}

.input-counter {
	position: absolute;
	right: 20rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 24rpx;
	color: #999;
}

.error-text {
	display: block;
	font-size: 24rpx;
	color: #dd524d;
	margin-top: 10rpx;
}

.input-tip {
	display: block;
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

/* 提交按钮样式 */
.action-section {
	margin-bottom: 40rpx;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
	text-align: center;
}

.submit-btn.submitting {
	background: linear-gradient(135deg, #999 0%, #666 100%);
}

.submit-btn.disabled {
	background: #c0c0c0;
	color: #999;
}

/* 结果显示区域样式 */
.result-section {
	margin-bottom: 40rpx;
}

.result-card {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.result-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.result-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.result-content {
	margin-bottom: 30rpx;
}

.result-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
}

.result-item:last-child {
	margin-bottom: 0;
}

.result-label {
	font-size: 28rpx;
	color: #666;
	margin-right: 20rpx;
	min-width: 120rpx;
}

.result-value {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	flex: 1;
}

/* 二维码相关样式 */
.qr-code-item {
	flex-direction: column;
	align-items: center;
	width: 100%;
	text-align: center;
}

.qr-code-item .result-label {
	margin-bottom: 10rpx;
	text-align: center;
	width: 100%;
}

.qr-code-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 100%;
	margin-top: 15rpx;
	padding: 30rpx 20rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	border: 1rpx solid #e9ecef;
}

/* 喵厂名称样式 */
.miaochang-name {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 15rpx 20rpx;
	background-color: #fff;
	border-radius: 8rpx;
	border: 1rpx solid #dee2e6;
	box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.miaochang-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.miaochang-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
}

.qr-code-image {
	width: 360rpx;
	height: 360rpx;
	border-radius: 12rpx;
	background-color: #fff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	display: block;
	margin: 0 auto;
}

.qr-code-tip {
	font-size: 24rpx;
	color: #666;
	margin-top: 15rpx;
	text-align: center;
	width: 100%;
}

.result-actions {
	display: flex;
	gap: 15rpx;
	flex-wrap: wrap;
}

.copy-btn, .share-btn, .save-btn {
	flex: 1;
	min-width: 140rpx;
	height: 70rpx;
	line-height: 70rpx;
	font-size: 26rpx;
	border-radius: 35rpx;
	border: none;
	text-align: center;
}

.copy-btn {
	background-color: #667eea;
	color: #fff;
}

.share-btn {
	background-color: #f0f0f0;
	color: #333;
}

.save-btn {
	background-color: #28a745;
	color: #fff;
}

/* 返回按钮样式 */
.back-section {
	text-align: center;
}

.back-btn {
	width: 200rpx;
	height: 70rpx;
	line-height: 70rpx;
	background-color: rgba(255, 255, 255, 0.2);
	color: #fff;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* 认证失败状态样式 */
.auth-failed {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	margin: 40rpx;
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}

.error-text {
	font-size: 32rpx;
	color: #dd524d;
	font-weight: bold;
	margin-bottom: 20rpx;
	text-align: center;
}

.error-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 40rpx;
	text-align: center;
}

.retry-btn {
	width: 200rpx;
	height: 70rpx;
	line-height: 70rpx;
	background-color: #667eea;
	color: #fff;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: none;
}
</style>
