<template>
	<view class="container">
		<!-- 页面标题 -->
		<view class="header">
			<text class="title">试水苗验证</text>
		</view>

		<!-- 等待认证状态 -->
		<view v-if="!authReady" class="auth-waiting">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ authWaitingMessage }}</text>
			</view>
		</view>

		<!-- 页面内容 - 只有认证成功后才显示 -->
		<view v-if="showPageContent" class="content">
			<!-- 权限限制提示 -->
			<view v-if="!canVerifyQrCode" class="permission-denied">
				<view class="denied-container">
					<text class="denied-icon">🚫</text>
					<text class="denied-title">仅限普通用户领取</text>
					<text class="denied-desc">您的账户角色不允许验证试水苗二维码</text>
				</view>

				<view class="action-section">
					<button class="back-btn" @click="goBack">返回首页</button>
				</view>
			</view>

			<!-- 验证中状态 -->
			<view v-else-if="loading" class="loading-section">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在验证二维码...</text>
			</view>

			<!-- 验证成功状态 -->
			<view v-if="verifyResult && !loading && !confirmed" class="verify-success">
				<view class="info-card">
					<view class="info-item">
						<text class="info-label">苗场名称：</text>
						<text class="info-value">{{ verifyResult.miaochang && verifyResult.miaochang.name || '未知' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">数量：</text>
						<text class="info-value">{{ verifyResult.quantity || 0 }}</text>
					</view>
				</view>

				<view class="action-section">
					<button 
						class="confirm-btn" 
						@click="confirmQrCode"
						:disabled="confirming"
					>
						{{ confirming ? '确认中...' : '确认' }}
					</button>
				</view>
			</view>

			<!-- 确认成功状态 -->
			<view v-if="confirmed" class="confirm-success">
				<view class="success-container">
					<text class="success-icon">✅</text>
					<text class="success-text">试水苗验证确认成功！</text>
					<text class="success-desc">操作已完成</text>
				</view>

				<view class="action-section">
					<button class="back-btn" @click="goBack">返回首页</button>
				</view>
			</view>

			<!-- 错误状态 -->
			<view v-if="errorMessage && !loading" class="error-section">
				<view class="error-container">
					<text class="error-icon">⚠️</text>
					<text class="error-text">{{ errorMessage }}</text>
				</view>

				<view class="action-section">
					<button class="retry-btn" @click="retryVerify" v-if="qrNo">重试验证</button>
					<button class="back-btn" @click="goBack">返回首页</button>
				</view>
			</view>
		</view>

		<!-- 认证失败状态 -->
		<view v-if="authReady && !authSuccess" class="auth-failed">
			<view class="error-container">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{ authError || '登录验证失败' }}</text>
				<text class="error-desc">无法验证试水苗二维码，请重新登录</text>
				<button class="retry-btn" @click="goBack">返回首页</button>
			</view>
		</view>
	</view>
</template>

<script>
import { preSeedAPI } from '../../../api/auth'
import pageEventMixin from '@/mixins/pageEvent.js'
import { mapState } from 'vuex'

export default {
	name: 'PreseedVerifyQrCodePage',
	mixins: [pageEventMixin],

	data() {
		return {
			qrNo: '',              // 二维码编号
			verifyResult: null,    // 验证结果
			loading: false,        // 是否正在验证
			confirming: false,     // 是否正在确认
			confirmed: false,      // 是否已确认
			errorMessage: ''       // 错误信息
		}
	},

	computed: {
		...mapState('user', ['userInfo']),

		/**
		 * 检查当前用户是否可以验证二维码
		 * 角色代码为5555或8888的用户不能验证二维码
		 */
		canVerifyQrCode() {
			if (!this.userInfo || !this.userInfo.role) {
				return true // 如果没有角色信息，默认可以验证
			}

			// 检查用户的角色代码
			const userRoleCode = this.userInfo.role.code
			console.log('🔐 检查用户验证权限:', this.userInfo.nickname, '角色代码:', userRoleCode)

			// 角色代码为5555或8888的用户不能验证二维码
			return userRoleCode !== 5555 && userRoleCode !== 8888
		}
	},

	onLoad(options) {
		console.log('试水苗验证页面加载，参数:', options)
		// pageEventMixin 会自动保存参数并设置事件监听
	},

	methods: {
		/**
		 * 解析二维码编号
		 */
		parseQrNo(options) {
			let extractedNo = ''

			// 方式1: 直接从no参数获取
			if (options.no) {
				extractedNo = options.no
				console.log('✅ 从no参数获取编号:', extractedNo)
			}
			// 方式2: 从scene参数解析 (微信小程序码扫码)
			else if (options.scene) {
				console.log('🔍 检测到scene参数:', options.scene)

				try {
					// 解码scene参数 (可能被URL编码)
					const decodedScene = decodeURIComponent(options.scene)
					console.log('🔓 解码后的scene:', decodedScene)

					// 解析 "no=ABC123" 格式
					const noMatch = decodedScene.match(/no=([^&]+)/)
					if (noMatch && noMatch[1]) {
						extractedNo = noMatch[1]
						console.log('✅ 从scene参数解析出编号:', extractedNo)
					} else {
						console.warn('⚠️ scene参数格式不正确:', decodedScene)
					}
				} catch (error) {
					console.error('❌ 解析scene参数失败:', error)
				}
			}

			if (!extractedNo) {
				console.warn('⚠️ 未找到有效的试水苗编号')
				this.errorMessage = '无效的二维码，缺少编号信息'
				return null
			}

			return extractedNo
		},

		/**
		 * 验证试水苗二维码
		 */
		async verifyQrCode(no) {
			if (!this.canMakeNetworkRequest) {
				console.warn('🚫 认证未完成，无法验证二维码')
				this.errorMessage = '认证未完成，无法验证二维码'
				return
			}

			// 防止重复请求
			if (this.loading) {
				console.warn('🚫 正在验证中，请勿重复请求')
				return
			}

			// 清除之前的状态
			this.errorMessage = ''
			this.verifyResult = null
			this.confirmed = false

			try {
				this.loading = true

				console.log('🔍 开始验证试水苗二维码:', no)

				// 调用验证API
				const response = await this.safeNetworkRequest(preSeedAPI.verifyQrcode, no)

				console.log('📥 验证API响应:', response)

				if (response.code === 200) {
					this.verifyResult = response.results
					console.log('✅ 验证成功:', this.verifyResult)
					console.log('🏢 苗场名称:', this.verifyResult.miaochang && this.verifyResult.miaochang.name)
					console.log('📊 数量:', this.verifyResult.quantity)

					uni.showToast({
						title: '验证成功',
						icon: 'success'
					})
				} else {
					// 显示错误信息
					this.errorMessage = response.msg || '验证失败'
					uni.showToast({
						title: response.msg || '验证失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('验证试水苗二维码失败:', error)
				this.errorMessage = '网络错误，请重试'
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		/**
		 * 确认试水苗操作
		 */
		async confirmQrCode() {
			if (!this.canMakeNetworkRequest) {
				console.warn('🚫 认证未完成，无法确认操作')
				this.errorMessage = '认证未完成，无法确认操作'
				return
			}

			if (!this.verifyResult) {
				console.warn('🚫 没有验证结果，无法确认')
				this.errorMessage = '没有验证结果，无法确认'
				return
			}

			// 防止重复请求
			if (this.confirming) {
				console.warn('🚫 正在确认中，请勿重复请求')
				return
			}

			try {
				this.confirming = true
				this.errorMessage = ''

				console.log('✅ 开始确认试水苗操作:', this.verifyResult)

				// 调用确认API
				const response = await this.safeNetworkRequest(preSeedAPI.confirmQrcode, this.verifyResult)

				console.log('📥 确认API响应:', response)

				if (response.code === 200) {
					this.confirmed = true
					console.log('✅ 确认成功')

					uni.showToast({
						title: '确认成功',
						icon: 'success'
					})
				} else {
					// 显示错误信息
					this.errorMessage = response.msg || '确认失败'
					uni.showToast({
						title: response.msg || '确认失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('确认试水苗操作失败:', error)
				this.errorMessage = '网络错误，请重试'
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				this.confirming = false
			}
		},

		/**
		 * 重试验证
		 */
		retryVerify() {
			if (this.qrNo) {
				this.verifyQrCode(this.qrNo)
			}
		},

		/**
		 * 返回首页
		 */
		goBack() {
			uni.reLaunch({
				url: '/pages/index/index'
			})
		},

		/**
		 * 认证完成回调 - 由 App.vue 事件触发
		 * 只有认证成功后才会执行
		 */
		onAuthReady(authData) {
			console.log('🔍 试水苗验证页面收到认证完成事件:', authData)
			console.log('✅ 页面现在可以安全地验证二维码')

			// 检查用户权限
			if (!this.canVerifyQrCode) {
				console.warn('⚠️ 用户角色不允许验证试水苗二维码')
				const roleCode = this.userInfo?.role?.code
				console.log('🔐 用户角色代码:', roleCode)
				return // 直接返回，不执行验证逻辑
			}

			// 认证完成后安全地处理页面参数
			if (this.pageOptions) {
				const no = this.parseQrNo(this.pageOptions)
				if (no) {
					this.qrNo = no
					console.log('🔍 开始验证试水苗二维码:', this.qrNo)
					this.verifyQrCode(this.qrNo)
				}
			} else {
				console.log('📝 没有试水苗二维码参数')
				this.errorMessage = '缺少二维码参数'
			}
		},

		/**
		 * 认证失败回调 - 由 App.vue 事件触发
		 */
		onAuthFailed(errorData) {
			console.log('🔍 试水苗验证页面收到认证失败事件:', errorData)
			console.log('❌ 页面无法验证二维码，显示错误状态')

			// 认证失败时的错误信息已经由 mixin 处理
			// 页面内容已经被隐藏，显示错误状态
		}
	}
}
</script>

<style scoped>
.container {
	padding: 40rpx;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
}

/* 认证等待状态 */
.auth-waiting {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400rpx;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 30rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid #e0e0e0;
	border-top: 6rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 32rpx;
	color: #666;
}

/* 页面内容 */
.content {
	display: flex;
	flex-direction: column;
	gap: 40rpx;
}

/* 权限限制提示 */
.permission-denied {
	display: flex;
	flex-direction: column;
	gap: 40rpx;
}

.denied-container {
	background-color: #fff2f0;
	border: 2rpx solid #ffccc7;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	text-align: center;
}

.denied-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
	display: block;
}

.denied-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #ff4d4f;
	margin-bottom: 20rpx;
	display: block;
}

.denied-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	display: block;
}

/* 加载状态 */
.loading-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 30rpx;
	padding: 80rpx 0;
}

/* 验证成功状态 */
.verify-success {
	display: flex;
	flex-direction: column;
	gap: 40rpx;
}

.info-card {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
	border-bottom: none;
}

.info-label {
	font-size: 32rpx;
	color: #666;
	font-weight: 500;
}

.info-value {
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
}

/* 确认成功状态 */
.confirm-success {
	display: flex;
	flex-direction: column;
	gap: 40rpx;
}

.success-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20rpx;
	padding: 60rpx 40rpx;
	background: white;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.success-icon {
	font-size: 80rpx;
}

.success-text {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.success-desc {
	font-size: 28rpx;
	color: #666;
}

/* 错误状态 */
.error-section, .auth-failed {
	display: flex;
	flex-direction: column;
	gap: 40rpx;
}

.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20rpx;
	padding: 60rpx 40rpx;
	background: white;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.error-icon {
	font-size: 80rpx;
}

.error-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff4757;
	text-align: center;
}

.error-desc {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

/* 按钮样式 */
.action-section {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.confirm-btn {
	background: #007aff;
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 30rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.confirm-btn:disabled {
	background: #ccc;
	color: #999;
}

.retry-btn {
	background: #ff6b35;
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 30rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.back-btn {
	background: #666;
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 30rpx;
	font-size: 32rpx;
	font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.container {
		padding: 30rpx;
	}

	.info-card, .success-container, .error-container {
		padding: 30rpx;
	}

	.title {
		font-size: 42rpx;
	}
}
</style>
