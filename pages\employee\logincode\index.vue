<template>
	<view class="logincode-page">
		<!-- 员工信息展示 -->
		<view class="employee-info-section">
			<view class="employee-card">
				<!-- 员工头像 -->
				<image
					class="employee-avatar"
					:src="employee.avatar || '/static/default-avatar.png'"
					mode="aspectFill"
				/>
				
				<!-- 员工基本信息 -->
				<view class="employee-details">
					<text class="employee-name">{{ employee.nickname || '未设置昵称' }}</text>
					<text class="employee-phone">{{ employee.employeePhone || '未设置电话' }}</text>
				</view>
			</view>
		</view>

		<!-- 登录码按钮 -->
		<view class="action-section">
			<button 
				class="login-code-btn"
				:disabled="loading"
				@click="generateLoginCode"
			>
				<text v-if="loading">生成中...</text>
				<text v-else>生成登录码</text>
			</button>
		</view>

		<!-- 二维码显示区域 -->
		<view v-if="qrCodeUrl" class="qrcode-section">
			<view class="qrcode-container">
				<text class="qrcode-title">员工登录二维码</text>
				<image 
					class="qrcode-image"
					:src="qrCodeUrl"
					mode="aspectFit"
					@error="onQrCodeError"
				/>
				<text class="qrcode-tip">请使用此二维码进行登录</text>
			</view>
		</view>

		<!-- 错误提示 -->
		<view v-if="errorMessage" class="error-section">
			<text class="error-text">{{ errorMessage }}</text>
			<button class="retry-btn" @click="generateLoginCode">重试</button>
		</view>
	</view>
</template>

<script>
import { userAPI } from '@/api/user.js'

export default {
	name: 'EmployeeLoginCodePage',

	data() {
		return {
			employee: {},           // 员工信息
			loading: false,         // 是否正在生成登录码
			qrCodeUrl: '',         // 二维码图片地址
			errorMessage: ''       // 错误信息
		}
	},

	onLoad(options) {
		console.log('📱 员工登录码页面加载，参数:', options)
		
		// 从URL参数中获取员工数据
		if (options.employee) {
			try {
				this.employee = JSON.parse(decodeURIComponent(options.employee))
				console.log('👤 接收到员工数据:', this.employee)
				console.log('🆔 员工ID:', this.employee.id)
				console.log('📱 员工昵称:', this.employee.nickname)
				console.log('📞 员工电话:', this.employee.employeePhone)
			} catch (error) {
				console.error('❌ 解析员工数据失败:', error)
				this.errorMessage = '员工数据解析失败'
				uni.showToast({
					title: '数据解析失败',
					icon: 'none'
				})
			}
		} else {
			console.error('❌ 未接收到员工数据')
			this.errorMessage = '未接收到员工数据'
			uni.showToast({
				title: '缺少员工数据',
				icon: 'none'
			})
		}
	},

	methods: {
		/**
		 * 生成员工登录码
		 */
		async generateLoginCode() {
			if (!this.employee.id) {
				console.error('❌ 员工ID不存在')
				this.errorMessage = '员工ID不存在'
				uni.showToast({
					title: '员工ID不存在',
					icon: 'none'
				})
				return
			}

			try {
				this.loading = true
				this.errorMessage = ''
				this.qrCodeUrl = ''
				
				console.log('🔄 开始生成员工登录码，员工ID:', this.employee.id)

				const response = await userAPI.getEmployeeLoginCode(this.employee.id)
				console.log('📥 登录码API响应:', response)

				if (response && response.code === 200) {
					const results = response.results || {}

					if (results.qrcodeUrl) {
						this.qrCodeUrl = results.qrcodeUrl
						console.log('✅ 登录码生成成功:', this.qrCodeUrl)

						uni.showToast({
							title: '登录码生成成功',
							icon: 'success'
						})
					} else {
						console.error('❌ API返回数据结构:', results)
						throw new Error('API返回数据中缺少二维码地址')
					}
				} else {
					throw new Error(response?.msg || '生成登录码失败')
				}
			} catch (error) {
				console.error('❌ 生成登录码失败:', error)
				this.errorMessage = error.message || '生成登录码失败'
				
				uni.showToast({
					title: this.errorMessage,
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		/**
		 * 二维码加载错误处理
		 */
		onQrCodeError() {
			console.error('❌ 二维码图片加载失败')
			this.errorMessage = '二维码图片加载失败'
			
			uni.showToast({
				title: '二维码加载失败',
				icon: 'none'
			})
		}
	}
}
</script>

<style scoped>
/* 页面容器 */
.logincode-page {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 40rpx;
}

/* 员工信息区域 */
.employee-info-section {
	margin-bottom: 60rpx;
}

.employee-card {
	background-color: white;
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.employee-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	margin-right: 30rpx;
	background-color: #f0f0f0;
}

.employee-details {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.employee-name {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 12rpx;
}

.employee-phone {
	font-size: 30rpx;
	color: #666;
}

/* 操作区域 */
.action-section {
	margin-bottom: 60rpx;
}

.login-code-btn {
	width: 100%;
	background-color: #007aff;
	color: white;
	border: none;
	border-radius: 16rpx;
	padding: 30rpx;
	font-size: 32rpx;
	font-weight: 600;
}

.login-code-btn[disabled] {
	background-color: #ccc;
	color: #999;
}

/* 二维码区域 */
.qrcode-section {
	margin-bottom: 40rpx;
}

.qrcode-container {
	background-color: white;
	border-radius: 20rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.qrcode-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	display: block;
}

.qrcode-image {
	width: 400rpx;
	height: 400rpx;
	border: 2rpx solid #eee;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
}

.qrcode-tip {
	font-size: 28rpx;
	color: #666;
	display: block;
}

/* 错误区域 */
.error-section {
	background-color: #fff2f0;
	border: 2rpx solid #ffccc7;
	border-radius: 16rpx;
	padding: 30rpx;
	text-align: center;
}

.error-text {
	font-size: 28rpx;
	color: #ff4d4f;
	margin-bottom: 20rpx;
	display: block;
}

.retry-btn {
	background-color: #ff4d4f;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}
</style>
