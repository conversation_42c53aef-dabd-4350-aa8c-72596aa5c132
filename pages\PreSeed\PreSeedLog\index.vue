<template>
	<view class="container">
		<!-- 认证等待状态 -->
		<view v-if="!showPageContent" class="auth-waiting">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ pageStatusText }}</text>
			</view>
		</view>

		<!-- 页面主要内容 - 只有认证成功后才显示 -->
		<view v-if="showPageContent" class="main-content">
			<view class="header">
				<text class="title">试水苗领取</text>
				<text class="subtitle">扫码领取试水苗</text>
			</view>

			<view class="content">
				<view class="qr-info" v-if="qrCode">
					<text class="info-text">试水苗二维码: {{ qrCode }}</text>
				</view>

				<view class="result" v-if="result">
					<text class="result-text">{{ result }}</text>
				</view>

				<button class="btn" @click="goBack">返回首页</button>
			</view>
		</view>

		<!-- 认证失败状态 -->
		<view v-if="authReady && !authSuccess" class="auth-failed">
			<view class="error-container">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{ authError || '登录验证失败' }}</text>
				<text class="error-desc">无法处理试水苗领取，请重新登录</text>
				<button class="retry-btn" @click="goBack">返回首页</button>
			</view>
		</view>
	</view>
</template>

<script>
import { preSeedAPI } from '../../../api/auth'
import pageEventMixin from '@/mixins/pageEvent.js'

export default {
	name: 'PreSeedLogPage',
	mixins: [pageEventMixin],
	data() {
		return {
			qrCode: '',
			result: ''
		}
	},

	onLoad(options) {
		console.log('试水苗页面加载，参数:', options)
		// pageEventMixin 会自动保存参数并设置事件监听
	},
	
	methods: {
		// 安全的处理试水苗领取 - 只有在认证成功后才能执行
		async handlePreSeedLog(code) {
			if (!this.canMakeNetworkRequest) {
				console.warn('🚫 认证未完成，无法处理试水苗领取')
				this.result = '认证未完成，无法处理试水苗领取'
				return
			}

			try {
				uni.showLoading({
					title: '处理中...'
				})

				console.log('🌱 开始安全的试水苗领取请求')

				// 使用安全的网络请求方法
				const response = await this.safeNetworkRequest(preSeedAPI.retrievePreSeedLog, code)

				if (response.code === 200) {
					this.result = '试水苗领取成功！'
					uni.showToast({
						title: '领取成功',
						icon: 'success'
					})
				} else {
					this.result = response.msg || '领取失败'
					uni.showToast({
						title: '领取失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('试水苗领取失败:', error)
				this.result = '网络错误，请重试'
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		
		// 返回首页
		goBack() {
			uni.reLaunch({
				url: '/pages/index/index'
			})
		},

		/**
		 * 认证完成回调 - 由 App.vue 事件触发
		 * 只有认证成功后才会执行
		 */
		onAuthReady(authData) {
			console.log('🌱 试水苗页面收到认证完成事件:', authData)
			console.log('✅ 试水苗页面现在可以安全地处理业务逻辑')

			// 认证完成后安全地处理页面参数
			if (this.pageOptions && this.pageOptions.code) {
				this.qrCode = this.pageOptions.code
				console.log('🔍 开始处理试水苗二维码:', this.qrCode)
				this.handlePreSeedLog(this.pageOptions.code)
			} else {
				console.log('📝 没有试水苗二维码参数')
			}
		},

		/**
		 * 认证失败回调 - 由 App.vue 事件触发
		 */
		onAuthFailed(errorData) {
			console.log('🌱 试水苗页面收到认证失败事件:', errorData)
			console.log('❌ 试水苗页面无法处理业务逻辑，显示错误状态')

			// 认证失败时的错误信息已经由 mixin 处理
			// 页面内容已经被隐藏，显示错误状态
		}
	}
}
</script>

<style>
.container {
	padding: 40rpx;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 48rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
}

.content {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.qr-info {
	margin-bottom: 40rpx;
	padding: 30rpx;
	background-color: #f0f0f0;
	border-radius: 10rpx;
}

.info-text {
	font-size: 28rpx;
	color: #333;
	word-break: break-all;
}

.result {
	margin-bottom: 40rpx;
	padding: 30rpx;
	background-color: #e8f5e8;
	border-radius: 10rpx;
	text-align: center;
}

.result-text {
	font-size: 32rpx;
	color: #4cd964;
}

.btn {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 44rpx;
	font-size: 32rpx;
	border: none;
}

/* 认证等待状态样式 */
.auth-waiting {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid #e0e0e0;
	border-top: 6rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

/* 认证失败状态样式 */
.auth-failed {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f8f8f8;
}

.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx;
	background-color: #fff;
	border-radius: 20rpx;
	margin: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}

.error-text {
	font-size: 28rpx;
	color: #dd524d;
	text-align: center;
	margin-bottom: 20rpx;
}

.error-desc {
	font-size: 24rpx;
	color: #999;
	text-align: center;
	margin-bottom: 40rpx;
}

.retry-btn {
	width: 200rpx;
	height: 70rpx;
	line-height: 70rpx;
	background-color: #007aff;
	color: #fff;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: none;
}

/* 主要内容样式调整 */
.main-content {
	min-height: 100vh;
}
</style>
