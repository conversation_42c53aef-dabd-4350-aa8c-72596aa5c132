/**
 * 环境配置文件
 * 根据不同环境配置不同的API地址和其他参数
 */

// 开发环境配置
const development = {
  BASE_URL: 'https://08c6108c858f.ngrok-free.app',
  TIMEOUT: 10000,
  DEBUG: true
}

// 测试环境配置
const testing = {
   BASE_URL: 'https://60cdb61acd03.ngrok-free.app',
  TIMEOUT: 10000,
  DEBUG: true
}

// 生产环境配置
const production = {
  BASE_URL: 'https://60cdb61acd03.ngrok-free.app',
  TIMEOUT: 8000,
  DEBUG: false
}

// 根据环境变量选择配置
const env = process.env.NODE_ENV || 'development'

const config = {
  development,
  testing,
  production
}

export default config[env]
