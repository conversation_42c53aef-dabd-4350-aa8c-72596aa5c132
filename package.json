{"name": "<PERSON><PERSON><PERSON>-we<PERSON>p", "version": "1.0.0", "description": "喵帮微信小程序", "main": "main.js", "scripts": {"serve": "npm run dev:mp-weixin", "build": "npm run build:mp-weixin", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build"}, "dependencies": {"vue": "^2.6.14", "vuex": "^3.6.2"}, "devDependencies": {"@dcloudio/uni-app": "^2.0.0", "@dcloudio/uni-cli-shared": "^2.0.0", "@dcloudio/uni-template-compiler": "^2.0.0", "@dcloudio/webpack-uni-mp-loader": "^2.0.0", "@dcloudio/webpack-uni-pages-loader": "^2.0.0", "cross-env": "^7.0.2", "mini-css-extract-plugin": "^0.9.0", "postcss-comment": "^2.0.0", "vue-template-compiler": "^2.6.14", "webpack": "^4.46.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}